"""
ETL Consolidado - Conexões Unificadas com Failover
Gerencia todas as conexões de banco de dados com pools, failover e retry automático.
"""

import time
import logging
from typing import Optional, Dict, Any, List, Union
from contextlib import contextmanager
from threading import Lock

try:
    import pymssql
    PYMSSQL_AVAILABLE = True
except ImportError:
    PYMSSQL_AVAILABLE = False

try:
    import psycopg2
    import psycopg2.pool
    PSYCOPG2_AVAILABLE = True
except ImportError:
    PSYCOPG2_AVAILABLE = False

try:
    import mysql.connector
    from mysql.connector import pooling
    MYSQL_AVAILABLE = True
except ImportError:
    MYSQL_AVAILABLE = False

from salesforce_integration.config import (
    DATABASE_CONFIGS,
    RETRY_CONFIGS,
    TIMEOUT_CONFIGS,
    LOGGING_CONFIG
)

# =============================================================================
# CONFIGURAÇÃO DE LOGGING
# =============================================================================

logger = logging.getLogger(__name__)

# =============================================================================
# CLASSES DE CONEXÃO
# =============================================================================

class DatabaseConnectionManager:
    """Gerenciador centralizado de conexões de banco de dados"""
    
    def __init__(self):
        self._pools: Dict[str, Any] = {}
        self._connections: Dict[str, Any] = {}
        self._lock = Lock()
        self._initialized = False
    
    def initialize(self) -> None:
        """Inicializa todos os pools de conexão"""
        if self._initialized:
            return
        
        with self._lock:
            if not self._initialized:
                logger.info("Inicializando pools de conexão...")
                
                # Inicializa cada pool de conexão
                for db_name, config in DATABASE_CONFIGS.items():
                    try:
                        self._create_connection_pool(db_name, config)
                        logger.info(f"✅ Pool de conexão '{db_name}' inicializado com sucesso")
                    except Exception as e:
                        logger.error(f"❌ Erro ao inicializar pool '{db_name}': {e}")
                        # Não faz raise para permitir que outros pools sejam inicializados
                
                self._initialized = True
                logger.info("✅ Todos os pools de conexão foram inicializados")
    
    def _create_connection_pool(self, db_name: str, config: Dict[str, Any]) -> None:
        """Cria pool de conexão específico para cada tipo de banco"""
        db_type = config.get('type')
        
        if db_type == 'mssql':
            # SQL Server não tem pool nativo, gerenciamos manualmente
            if not PYMSSQL_AVAILABLE:
                raise ImportError("pymssql não está instalado")
            
            self._pools[db_name] = {
                'type': 'mssql',
                'config': config,
                'active_connections': [],
                'max_connections': config.get('pool_size', 15)
            }
        
        elif db_type == 'postgresql':
            # PostgreSQL com psycopg2 pool
            if not PSYCOPG2_AVAILABLE:
                raise ImportError("psycopg2 não está instalado")
            
            self._pools[db_name] = psycopg2.pool.ThreadedConnectionPool(
                minconn=1,
                maxconn=config.get('pool_size', 15),
                host=config['host'],
                port=config['port'],
                database=config['database'],
                user=config['username'],
                password=config['password']
            )
        
        elif db_type == 'mysql':
            # MySQL com pool nativo
            if not MYSQL_AVAILABLE:
                raise ImportError("mysql-connector-python não está instalado")
            
            pool_config = {
                'pool_name': f'{db_name}_pool',
                'pool_size': config.get('pool_size', 15),
                'pool_reset_session': True,
                'host': config['host'],
                'port': config['port'],
                'database': config['database'],
                'user': config['username'],
                'password': config['password']
            }
            self._pools[db_name] = mysql.connector.pooling.MySQLConnectionPool(**pool_config)
        
        else:
            raise ValueError(f"Tipo de banco não suportado: {db_type}")
    
    def get_connection(self, db_name: str) -> Any:
        """Obtém conexão do pool com retry automático"""
        config = DATABASE_CONFIGS.get(db_name)
        if not config:
            raise ValueError(f"Configuração de banco '{db_name}' não encontrada")

        # Inicializa pool específico se não existir
        if db_name not in self._pools:
            try:
                self._create_connection_pool(db_name, config)
                logger.info(f"✅ Pool de conexão '{db_name}' inicializado sob demanda")
            except Exception as e:
                logger.error(f"❌ Erro ao inicializar pool '{db_name}': {e}")
                raise

        return self._get_connection_with_retry(db_name, config)
    
    def _get_connection_with_retry(self, db_name: str, config: Dict[str, Any]) -> Any:
        """Obtém conexão com retry automático e failover"""
        max_attempts = config.get('retry_attempts', RETRY_CONFIGS['max_attempts'])
        base_delay = config.get('retry_delay', RETRY_CONFIGS['base_delay'])
        
        for attempt in range(max_attempts):
            try:
                # Tenta conexão primária
                connection = self._get_single_connection(db_name, config, 'primary')
                if connection:
                    return connection
                
                # Se NewCon, tenta failover para secundário
                if db_name == 'newcon' and 'secondary' in config:
                    logger.warning(f"Tentando failover para servidor secundário (tentativa {attempt + 1})")
                    connection = self._get_single_connection(db_name, config, 'secondary')
                    if connection:
                        return connection
                
            except Exception as e:
                logger.warning(f"Tentativa {attempt + 1} falhou para '{db_name}': {e}")
                
                if attempt < max_attempts - 1:
                    delay = base_delay * (2 ** attempt)  # Exponential backoff
                    logger.info(f"Aguardando {delay}s antes da próxima tentativa...")
                    time.sleep(delay)
                else:
                    logger.error(f"❌ Todas as tentativas falharam para '{db_name}'")
                    raise
        
        raise Exception(f"Não foi possível conectar ao banco '{db_name}' após {max_attempts} tentativas")
    
    def _get_single_connection(self, db_name: str, config: Dict[str, Any], server_type: str = 'primary') -> Any:
        """Obtém uma única conexão do pool"""
        db_type = config.get('type')
        server_config = config.get(server_type, config)
        
        if db_type == 'mssql':
            if not PYMSSQL_AVAILABLE:
                raise ImportError("pymssql não está instalado")
            return self._get_mssql_connection(db_name, server_config)
        elif db_type == 'postgresql':
            if not PSYCOPG2_AVAILABLE:
                raise ImportError("psycopg2 não está instalado")
            return self._pools[db_name].getconn()
        elif db_type == 'mysql':
            if not MYSQL_AVAILABLE:
                raise ImportError("mysql-connector-python não está instalado")
            return self._pools[db_name].get_connection()
        else:
            raise ValueError(f"Tipo de banco não suportado: {db_type}")
    
    def _get_mssql_connection(self, db_name: str, server_config: Dict[str, Any]) -> Any:
        """Obtém conexão SQL Server com gerenciamento manual de pool"""
        if not PYMSSQL_AVAILABLE:
            raise ImportError("pymssql não está instalado")
        
        pool = self._pools[db_name]
        
        # Verifica se há conexões disponíveis
        if len(pool['active_connections']) < pool['max_connections']:
            connection = pymssql.connect(
                server=server_config['host'],
                port=server_config['port'],
                database=server_config['database'],
                user=server_config['username'],
                password=server_config['password'],
                timeout=server_config.get('timeout', 30)
            )
            pool['active_connections'].append(connection)
            return connection
        else:
            # Reutiliza conexão existente (implementação básica)
            return pool['active_connections'][0]
    
    def return_connection(self, db_name: str, connection: Any) -> None:
        """Retorna conexão para o pool"""
        if not connection:
            return
        
        config = DATABASE_CONFIGS.get(db_name, {})
        db_type = config.get('type')
        
        try:
            if db_type == 'postgresql':
                self._pools[db_name].putconn(connection)
            elif db_type == 'mysql':
                connection.close()  # MySQL pool gerencia automaticamente
            elif db_type == 'mssql':
                # Para SQL Server, apenas mantemos a conexão no pool
                pass
            
            logger.debug(f"Conexão retornada para o pool '{db_name}'")
        except Exception as e:
            logger.warning(f"Erro ao retornar conexão para '{db_name}': {e}")
    
    def close_connection(self, db_name: str, connection: Any) -> None:
        """Fecha conexão específica"""
        if not connection:
            return
        
        try:
            connection.close()
            logger.debug(f"Conexão fechada para '{db_name}'")
        except Exception as e:
            logger.warning(f"Erro ao fechar conexão '{db_name}': {e}")
    
    def close_all_connections(self) -> None:
        """Fecha todas as conexões e pools"""
        logger.info("Fechando todas as conexões...")
        
        for db_name, pool in self._pools.items():
            try:
                config = DATABASE_CONFIGS.get(db_name, {})
                db_type = config.get('type')
                
                if db_type == 'postgresql':
                    pool.closeall()
                elif db_type == 'mysql':
                    # MySQL pool fecha automaticamente
                    pass
                elif db_type == 'mssql':
                    # Fecha conexões SQL Server manualmente
                    for conn in pool['active_connections']:
                        conn.close()
                    pool['active_connections'].clear()
                
                logger.info(f"✅ Pool '{db_name}' fechado com sucesso")
            except Exception as e:
                logger.error(f"❌ Erro ao fechar pool '{db_name}': {e}")
        
        self._pools.clear()
        self._initialized = False
        logger.info("✅ Todas as conexões foram fechadas")
    
    def test_connection(self, db_name: str) -> bool:
        """Testa conexão específica"""
        try:
            connection = self.get_connection(db_name)
            cursor = connection.cursor()
            
            # Query simples para testar
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
            
            cursor.close()
            self.return_connection(db_name, connection)
            
            return result is not None
        except Exception as e:
            logger.error(f"❌ Teste de conexão falhou para '{db_name}': {e}")
            return False
    
    def test_all_connections(self) -> Dict[str, bool]:
        """Testa todas as conexões"""
        results = {}
        logger.info("Testando todas as conexões...")
        
        for db_name in DATABASE_CONFIGS.keys():
            logger.info(f"Testando conexão '{db_name}'...")
            results[db_name] = self.test_connection(db_name)
            
            if results[db_name]:
                logger.info(f"✅ Conexão '{db_name}' OK")
            else:
                logger.error(f"❌ Conexão '{db_name}' FALHOU")
        
        success_count = sum(1 for success in results.values() if success)
        total_count = len(results)
        
        logger.info(f"Resultado: {success_count}/{total_count} conexões bem-sucedidas")
        
        return results
    
    def get_connection_stats(self) -> Dict[str, Any]:
        """Retorna estatísticas dos pools de conexão"""
        stats = {}
        
        for db_name, pool in self._pools.items():
            config = DATABASE_CONFIGS.get(db_name, {})
            db_type = config.get('type')
            
            if db_type == 'mssql':
                stats[db_name] = {
                    'type': 'mssql',
                    'active_connections': len(pool['active_connections']),
                    'max_connections': pool['max_connections'],
                    'available_connections': pool['max_connections'] - len(pool['active_connections'])
                }
            else:
                stats[db_name] = {
                    'type': db_type,
                    'status': 'active'
                }
        
        return stats

# =============================================================================
# INSTÂNCIA GLOBAL DO GERENCIADOR
# =============================================================================

db_manager = DatabaseConnectionManager()

# =============================================================================
# CONTEXT MANAGERS
# =============================================================================

@contextmanager
def get_database_connection(db_name: str):
    """Context manager para obter conexão de banco"""
    connection = None
    try:
        connection = db_manager.get_connection(db_name)
        yield connection
    finally:
        if connection:
            db_manager.return_connection(db_name, connection)

@contextmanager
def get_database_cursor(db_name: str):
    """Context manager para obter cursor de banco"""
    connection = None
    cursor = None
    try:
        connection = db_manager.get_connection(db_name)
        cursor = connection.cursor()
        yield cursor
    finally:
        if cursor:
            cursor.close()
        if connection:
            db_manager.return_connection(db_name, connection)

# =============================================================================
# FUNÇÕES DE CONVENIÊNCIA
# =============================================================================

def get_newcon_connection():
    """Obtém conexão NewCon com failover automático"""
    return db_manager.get_connection('newcon')

def get_dw_corporativo_connection():
    """Obtém conexão DW Corporativo"""
    return db_manager.get_connection('dw_corporativo')

def get_orbbits_connection():
    """Obtém conexão Orbbits"""
    return db_manager.get_connection('orbbits')

def initialize_all_connections():
    """Inicializa todas as conexões"""
    db_manager.initialize()

def test_all_connections() -> Dict[str, bool]:
    """Testa todas as conexões"""
    return db_manager.test_all_connections()

def close_all_connections():
    """Fecha todas as conexões"""
    db_manager.close_all_connections()

def get_connection_stats() -> Dict[str, Any]:
    """Retorna estatísticas das conexões"""
    return db_manager.get_connection_stats()

# =============================================================================
# FUNÇÕES DE QUERY AUXILIARES
# =============================================================================

def execute_query(db_name: str, query: str, params: Optional[tuple] = None) -> List[tuple]:
    """Executa query e retorna resultados"""
    with get_database_cursor(db_name) as cursor:
        cursor.execute(query, params or ())
        return cursor.fetchall()

def execute_query_single(db_name: str, query: str, params: Optional[tuple] = None) -> Optional[tuple]:
    """Executa query e retorna resultado único"""
    with get_database_cursor(db_name) as cursor:
        cursor.execute(query, params or ())
        return cursor.fetchone()

def execute_query_dict(db_name: str, query: str, params: Optional[tuple] = None) -> List[Dict[str, Any]]:
    """Executa query e retorna resultados como dicionário"""
    with get_database_cursor(db_name) as cursor:
        cursor.execute(query, params or ())
        columns = [desc[0] for desc in cursor.description]
        return [dict(zip(columns, row)) for row in cursor.fetchall()]

# =============================================================================
# VALIDAÇÃO E DIAGNÓSTICO
# =============================================================================

def validate_database_config(db_name: str) -> tuple:
    """Valida configuração de banco específico"""
    config = DATABASE_CONFIGS.get(db_name)
    
    if not config:
        return False, f"Configuração '{db_name}' não encontrada"
    
    required_fields = ['type', 'host', 'database', 'username', 'password']
    missing_fields = []
    
    # Verifica campos obrigatórios (considerando estrutura primary/secondary)
    primary_config = config.get('primary', config)
    
    # Para NewCon, verifica se tem estrutura primary/secondary correta
    if 'primary' in config and 'secondary' in config:
        for field in required_fields:
            if field not in primary_config or not primary_config[field]:
                missing_fields.append(field)
    else:
        # Para outros bancos, verifica diretamente
        for field in required_fields:
            if field not in config or not config[field]:
                missing_fields.append(field)
    
    if missing_fields:
        return False, f"Campos obrigatórios ausentes: {', '.join(missing_fields)}"
    
    return True, "Configuração válida"

def diagnose_connection_issues(db_name: str) -> Dict[str, Any]:
    """Diagnostica problemas de conexão"""
    # TODO: AGENTE_2 implementar diagnóstico detalhado
    # TODO: AGENTE_3 adicionar diagnóstico de performance
    # TODO: AGENTE_4 adicionar diagnóstico de volume de dados
    
    diagnosis = {
        'database': db_name,
        'config_valid': False,
        'connection_test': False,
        'issues': [],
        'recommendations': []
    }
    
    # Valida configuração
    is_valid, message = validate_database_config(db_name)
    diagnosis['config_valid'] = is_valid
    
    if not is_valid:
        diagnosis['issues'].append(f"Configuração inválida: {message}")
        diagnosis['recommendations'].append("Verificar variáveis de ambiente")
    
    # Testa conexão
    if is_valid:
        diagnosis['connection_test'] = db_manager.test_connection(db_name)
        
        if not diagnosis['connection_test']:
            diagnosis['issues'].append("Falha na conexão com o banco")
            diagnosis['recommendations'].append("Verificar rede e credenciais")
    
    return diagnosis

# =============================================================================
# INICIALIZAÇÃO E CLEANUP
# =============================================================================

def startup():
    """Função de inicialização"""
    logger.info("Iniciando sistema de conexões de banco...")
    initialize_all_connections()
    
    # Testa todas as conexões
    results = test_all_connections()
    failed_connections = [db for db, success in results.items() if not success]
    
    if failed_connections:
        logger.warning(f"Conexões com falha: {', '.join(failed_connections)}")
    else:
        logger.info("✅ Todas as conexões estão funcionando")

def shutdown():
    """Função de finalização"""
    logger.info("Finalizando sistema de conexões de banco...")
    close_all_connections()
    logger.info("✅ Sistema de conexões finalizado")

# =============================================================================
# MAIN PARA TESTES
# =============================================================================

if __name__ == "__main__":
    # Testa conexões
    import sys
    
    logging.basicConfig(level=logging.INFO)
    
    try:
        startup()
        
        # Testa cada conexão individualmente
        for db_name in DATABASE_CONFIGS.keys():
            print(f"\n{'='*50}")
            print(f"Testando {db_name.upper()}")
            print(f"{'='*50}")
            
            diagnosis = diagnose_connection_issues(db_name)
            
            print(f"Config válida: {diagnosis['config_valid']}")
            print(f"Conexão OK: {diagnosis['connection_test']}")
            
            if diagnosis['issues']:
                print(f"Problemas: {', '.join(diagnosis['issues'])}")
            
            if diagnosis['recommendations']:
                print(f"Recomendações: {', '.join(diagnosis['recommendations'])}")
        
        # Estatísticas finais
        print(f"\n{'='*50}")
        print("ESTATÍSTICAS FINAIS")
        print(f"{'='*50}")
        
        stats = get_connection_stats()
        for db_name, stat in stats.items():
            print(f"{db_name}: {stat}")
        
    except Exception as e:
        logger.error(f"❌ Erro durante teste: {e}")
        sys.exit(1)
    finally:
        shutdown()