"""
DAG V4 Silver - Gerador Dinâmico de DAGs

Esta DAG gera automaticamente DAGs silver para cada sistema configurado,
com processamento paralelo de transformações respeitando dependências.

Sistemas Suportados:
- Syonet (SQL Server)
- Oracle ERP (Oracle) - futuro
- Sistema X (PostgreSQL) - futuro

Características:
- Processamento paralelo respeitando dependências
- Transformações CREATE TABLE AS dentro do DW
- Configurações específicas por sistema
- Validação automática de dependências bronze
- Aplicação automática de permissões
"""

from datetime import datetime, timedelta
from airflow import DAG
from airflow.operators.python import PythonOperator
from airflow.operators.dummy import DummyOperator
from airflow.sensors.external_task import ExternalTaskSensor
from airflow.exceptions import AirflowSkipException
from typing import Dict, Any, List

# Imports do ETL Framework
from etl_framework.silver.processor import create_silver_processor
from etl_framework.config.dw_config import create_corporate_dw_config


# Configuração padrão das DAGs
default_args = {
    'owner': 'data-engineering',
    'depends_on_past': False,
    'start_date': datetime(2024, 1, 1),
    'email_on_failure': True,
    'email_on_retry': False,
    'retries': 2,
    'retry_delay': timedelta(minutes=5),
    'catchup': False
}

# Configuração dos sistemas
SYSTEMS_CONFIG = {
    'syonet': {
        'description': 'Sistema Syonet - Transformações Silver (CREATE TABLE AS no DW)',
        'schedule_interval': '0 8 * * *',  # 8h da manhã (após bronze)
        'max_active_runs': 1,
        'tags': ['syonet', 'silver', 'transformations', 'dw'],
        'bronze_dag_id': 'V4_BRONZE_SYONET'
    },
    # Futuro: Oracle ERP
    # 'oracle_erp': {
    #     'description': 'Sistema Oracle ERP - Transformações Silver',
    #     'schedule_interval': '0 9 * * *',  # 9h da manhã (após bronze)
    #     'max_active_runs': 1,
    #     'tags': ['oracle', 'silver', 'transformations', 'dw'],
    #     'bronze_dag_id': 'V4_BRONZE_ORACLE_ERP'
    # }
}


def create_process_transformation_function(system_name: str, transformation_name: str):
    """
    Cria função para processar uma transformação específica
    
    Args:
        system_name: Nome do sistema (syonet, oracle_erp, etc.)
        transformation_name: Nome da transformação
    """
    def process_transformation(**context):
        # Configuração única do DW corporativo
        dw_config = create_corporate_dw_config()
        
        # Cria processador silver específico para o sistema
        processor = create_silver_processor(system_name, dw_config)
        
        try:
            # Encontra a transformação
            transformation = None
            for t in processor.system_config.get_silver_transformations():
                if t.name == transformation_name:
                    transformation = t
                    break
            
            if not transformation:
                raise Exception(f"Transformação {transformation_name} não encontrada na configuração do {system_name}")
            
            # Verifica se deve executar hoje (para transformações semanais/mensais)
            if not transformation.should_execute_today():
                print(f"⏭️ {transformation_name} não deve ser executada hoje (frequência: {transformation.frequency.value})")
                raise AirflowSkipException(f"Transformação {transformation_name} pulada devido à frequência")
            
            # Processa a transformação
            result = processor.process_single_transformation(transformation)
            
            if result['success']:
                print(f"✅ {transformation_name} processada com sucesso!")
                print(f"📊 Registros: {result['records_processed']}")
                print(f"⏱️ Tempo: {result['execution_time']:.1f}s")
            else:
                raise Exception(f"Falha no processamento de {transformation_name}: {result.get('error')}")
            
            return result
            
        finally:
            processor.cleanup()
    
    return process_transformation


def create_validate_bronze_function(system_name: str):
    """
    Cria função para validar se bronze foi concluído
    """
    def validate_bronze_completion(**context):
        # Configuração única do DW corporativo
        dw_config = create_corporate_dw_config()
        
        # Cria processador para validação
        processor = create_silver_processor(system_name, dw_config)
        
        try:
            # Verifica se tabelas bronze principais existem e têm dados
            bronze_tables_to_check = []
            
            if system_name == 'syonet':
                bronze_tables_to_check = [
                    "bronze_syonet_syo_evento",
                    "bronze_syonet_syo_cliente", 
                    "bronze_syonet_syo_encaminhamento",
                    "bronze_syonet_syo_usuario"
                ]
            # Adicionar outros sistemas conforme necessário
            
            with processor.dw_connection.get_cursor() as (cursor, conn):
                for table_name in bronze_tables_to_check:
                    # Verifica se tabela existe
                    cursor.execute(f"""
                        SELECT EXISTS (
                            SELECT 1 FROM information_schema.tables 
                            WHERE table_name = '{table_name}'
                        )
                    """)
                    
                    if not cursor.fetchone()[0]:
                        raise Exception(f"Tabela bronze {table_name} não encontrada")
                    
                    # Verifica se tem dados
                    cursor.execute(f"SELECT COUNT(*) FROM dbdwcorporativo.{table_name}")
                    count = cursor.fetchone()[0]
                    
                    if count == 0:
                        raise Exception(f"Tabela bronze {table_name} está vazia")
                    
                    print(f"✅ {table_name}: {count:,} registros")
            
            print(f"✅ Validação de dependências bronze do {system_name} concluída")
            return True
            
        finally:
            processor.cleanup()
    
    return validate_bronze_completion


def create_generate_report_function(system_name: str):
    """
    Cria função para gerar relatório final
    """
    def generate_report(**context):
        # Coleta resultados das tasks anteriores
        task_instances = context['task_instance'].get_dag_run().get_task_instances()
        
        successful_transformations = []
        failed_transformations = []
        skipped_transformations = []
        total_records = 0
        total_time = 0
        
        for ti in task_instances:
            if ti.task_id.startswith('process_'):
                transformation_name = ti.task_id.replace('process_', '')
                
                if ti.state == 'success':
                    try:
                        result = ti.xcom_pull()
                        if result and result.get('success'):
                            successful_transformations.append(transformation_name)
                            total_records += result.get('records_processed', 0)
                            total_time += result.get('execution_time', 0)
                        else:
                            failed_transformations.append(transformation_name)
                    except:
                        failed_transformations.append(transformation_name)
                        
                elif ti.state == 'skipped':
                    skipped_transformations.append(transformation_name)
                else:
                    failed_transformations.append(transformation_name)
        
        # Gera relatório
        print(f"📊 RELATÓRIO FINAL SILVER - {system_name.upper()}")
        print("=" * 50)
        print(f"✅ Transformações processadas: {len(successful_transformations)}")
        print(f"⏭️ Transformações puladas: {len(skipped_transformations)}")
        print(f"❌ Transformações com falha: {len(failed_transformations)}")
        print(f"📈 Total de registros processados: {total_records:,}")
        print(f"⏱️ Tempo total de processamento: {total_time:.1f}s")
        
        if successful_transformations:
            print(f"\n✅ Sucessos: {', '.join(successful_transformations)}")
        
        if skipped_transformations:
            print(f"\n⏭️ Puladas: {', '.join(skipped_transformations)}")
        
        if failed_transformations:
            print(f"\n❌ Falhas: {', '.join(failed_transformations)}")
        
        print("=" * 50)
        
        return {
            'system': system_name,
            'successful_transformations': successful_transformations,
            'skipped_transformations': skipped_transformations,
            'failed_transformations': failed_transformations,
            'total_records': total_records,
            'total_time': total_time
        }
    
    return generate_report


def create_silver_dag(system_name: str, system_config: Dict[str, Any]) -> DAG:
    """
    Cria DAG silver para um sistema específico
    
    Args:
        system_name: Nome do sistema
        system_config: Configuração do sistema
        
    Returns:
        DAG: DAG configurada para o sistema
    """
    
    dag_id = f'V4_SILVER_{system_name.upper()}'
    
    dag = DAG(
        dag_id,
        default_args=default_args,
        description=system_config['description'],
        schedule_interval=system_config['schedule_interval'],
        catchup=False,
        max_active_runs=system_config['max_active_runs'],
        tags=system_config['tags']
    )
    
    # Task de início
    start_task = DummyOperator(
        task_id='start',
        dag=dag
    )
    
    # Sensor para aguardar bronze
    wait_for_bronze = ExternalTaskSensor(
        task_id='wait_for_bronze',
        external_dag_id=system_config['bronze_dag_id'],
        external_task_id='end',
        timeout=3600,  # 1 hora
        poke_interval=300,  # 5 minutos
        dag=dag
    )
    
    # Task de validação bronze
    validate_task = PythonOperator(
        task_id='validate_bronze_completion',
        python_callable=create_validate_bronze_function(system_name),
        dag=dag
    )
    
    # Obtém transformações e suas dependências
    dw_config = create_corporate_dw_config()
    processor = create_silver_processor(system_name, dw_config)
    
    # Transformações independentes (sem dependências silver)
    independent_transformations = processor.get_transformations_for_parallel_processing()
    dependency_map = processor.get_transformations_with_dependencies()
    
    processor.cleanup()
    
    # Cria tasks para transformações independentes (processamento paralelo)
    independent_tasks = []
    for transformation in independent_transformations:
        task = PythonOperator(
            task_id=f'process_{transformation.name}',
            python_callable=create_process_transformation_function(system_name, transformation.name),
            dag=dag
        )
        independent_tasks.append(task)
    
    # Cria tasks para transformações dependentes
    dependent_tasks = []
    all_transformations = processor.system_config.get_silver_transformations()
    
    for transformation in all_transformations:
        if transformation not in independent_transformations:
            task = PythonOperator(
                task_id=f'process_{transformation.name}',
                python_callable=create_process_transformation_function(system_name, transformation.name),
                dag=dag
            )
            dependent_tasks.append((task, transformation))
    
    # Task de relatório final
    report_task = PythonOperator(
        task_id='generate_report',
        python_callable=create_generate_report_function(system_name),
        dag=dag
    )
    
    # Task de fim
    end_task = DummyOperator(
        task_id='end',
        dag=dag
    )
    
    # Dependências básicas
    start_task >> wait_for_bronze >> validate_task >> independent_tasks
    
    # Configura dependências entre transformações
    task_map = {task.task_id.replace('process_', ''): task for task in independent_tasks}
    for task, transformation in dependent_tasks:
        task_map[transformation.name] = task
        
        # Configura dependências
        dependencies = dependency_map.get(transformation.name, [])
        if dependencies:
            dependency_tasks = [task_map[dep] for dep in dependencies if dep in task_map]
            if dependency_tasks:
                dependency_tasks >> task
            else:
                # Se não tem dependências válidas, executa após validação
                validate_task >> task
        else:
            validate_task >> task
    
    # Todas as tasks convergem para o relatório
    all_transformation_tasks = independent_tasks + [task for task, _ in dependent_tasks]
    all_transformation_tasks >> report_task >> end_task
    
    return dag


# Gera DAGs dinamicamente para cada sistema configurado
for system_name, system_config in SYSTEMS_CONFIG.items():
    try:
        # Cria DAG para o sistema
        dag = create_silver_dag(system_name, system_config)
        
        # Adiciona ao namespace global para que o Airflow encontre
        globals()[f'V4_SILVER_{system_name.upper()}'] = dag
        
        print(f"✅ DAG V4_SILVER_{system_name.upper()} criada com sucesso")
        
    except Exception as e:
        print(f"❌ Erro ao criar DAG para {system_name}: {str(e)}")
        # Não falha a importação, apenas loga o erro
