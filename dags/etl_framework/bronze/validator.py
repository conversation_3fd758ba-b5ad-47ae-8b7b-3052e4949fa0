"""
Bronze Validator - ETL Framework

Validador para verificar integridade de dados bronze.
"""

import logging
from typing import Dict, Any, <PERSON><PERSON>
from etl_framework.connections.base import DatabaseConnection
from etl_framework.config.system_config import SystemConfig
from etl_framework.config.dw_config import DWConfig
from etl_framework.utils.logger import ETLLogger


class BronzeValidator:
    """
    Validador para tabelas Bronze
    
    Verifica:
    - Contagem de registros entre origem e destino
    - Integridade de dados básica
    """
    
    def __init__(self, 
                 source_connection: DatabaseConnection,
                 dw_connection: DatabaseConnection,
                 system_config: SystemConfig,
                 dw_config: DWConfig):
        
        self.source_connection = source_connection
        self.dw_connection = dw_connection
        self.system_config = system_config
        self.dw_config = dw_config
        self.logger = logging.getLogger(f"BronzeValidator_{system_config.name}")
    
    def validate_table(self, table_config) -> <PERSON>ple[bool, Dict[str, Any]]:
        """
        Valida uma tabela bronze
        
        Returns:
            Tuple[bool, Dict]: (is_valid, validation_info)
        """
        try:
            # Conta registros na origem
            source_count = self._count_source_records(table_config)
            
            # Conta registros no destino
            dest_count = self._count_dest_records(table_config)
            
            # Validação básica de contagem
            is_valid = self._validate_counts(source_count, dest_count, table_config)
            
            validation_info = {
                'source_count': source_count,
                'dest_count': dest_count,
                'count_match': source_count == dest_count,
                'count_diff': abs(source_count - dest_count),
                'count_diff_percentage': abs(source_count - dest_count) / max(source_count, 1) * 100
            }
            
            return is_valid, validation_info
            
        except Exception as e:
            self.logger.error(f"Erro na validação de {table_config.name}: {str(e)}")
            return False, {'error': str(e)}
    
    def _count_source_records(self, table_config) -> int:
        """Conta registros na tabela de origem"""
        try:
            query = f"SELECT COUNT(*) FROM {table_config.source_schema}.{table_config.source_table}"
            
            with self.source_connection.get_cursor() as (cursor, conn):
                cursor.execute(query)
                result = cursor.fetchone()
                return result[0] if result else 0
                
        except Exception as e:
            self.logger.warning(f"Erro ao contar registros de origem para {table_config.name}: {str(e)}")
            return 0
    
    def _count_dest_records(self, table_config) -> int:
        """Conta registros na tabela de destino"""
        try:
            bronze_table = f"{self.dw_config.bronze_schema}.{self.system_config.bronze_prefix}{table_config.name}"
            query = f"SELECT COUNT(*) FROM {bronze_table}"
            
            with self.dw_connection.get_cursor() as (cursor, conn):
                cursor.execute(query)
                result = cursor.fetchone()
                return result[0] if result else 0
                
        except Exception as e:
            self.logger.warning(f"Erro ao contar registros de destino para {table_config.name}: {str(e)}")
            return 0
    
    def _validate_counts(self, source_count: int, dest_count: int, table_config) -> bool:
        """
        Valida se as contagens estão dentro dos limites aceitáveis
        """
        # Se não há dados na origem, destino deve estar vazio também
        if source_count == 0:
            return dest_count == 0
        
        # Para tabelas incrementais, destino pode ter mais registros (histórico)
        if hasattr(table_config, 'incremental_mode') and table_config.incremental_mode.value != 'full':
            return dest_count >= source_count
        
        # Para tabelas full load, contagens devem ser iguais (ou muito próximas)
        diff_percentage = abs(source_count - dest_count) / source_count * 100
        return diff_percentage <= 5.0  # Permite 5% de diferença