"""
Estratégia Incremental Diário - ETL Framework

Implementa carga incremental diária baseada na lógica das DAGs V3.
"""

from .base import ETLStrategy, ETLMode, TableMetadata, ETLResult
from typing import Dict, Any, Optional


class IncrementalDailyStrategy(ETLStrategy):
    """
    Estratégia de Incremental Diário
    
    Características:
    - Carrega apenas dados do dia atual
    - Usa filtros baseados em dt_inc/dt_alt
    - Executa DELETEs antes de INSERT (para reprocessamento)
    - Mais rápida para tabelas grandes
    """
    
    def get_mode(self) -> ETLMode:
        return ETLMode.INCREMENTAL_DAILY
    
    def should_use_strategy(self, table_metadata: TableMetadata, 
                          analysis_info: Optional[Dict[str, Any]] = None) -> bool:
        """
        Incremental diário é usado para:
        - Tabelas com campo ID (tabelas grandes)
        - Como primeira tentativa em estratégias inteligentes
        """
        return bool(table_metadata.id_field)
    
    def build_extract_query(self, table_metadata: TableMetadata) -> str:
        """Constrói query de extração incremental diária"""
        source_table = table_metadata.source_table or table_metadata.name
        select_fields = table_metadata.select_fields or "*"
        
        # Se tem SQL customizado para incremental, usar
        if (table_metadata.custom_sql and 
            'incremental' in table_metadata.custom_sql):
            return table_metadata.custom_sql['incremental']
        
        # Filtro incremental padrão (baseado nas DAGs V3)
        daily_filter = self._build_daily_filter()
        
        # Query baseada no tipo de conexão
        if hasattr(self.source_connection, 'execute_openquery'):
            # SQL Server com OPENQUERY
            return f"""
                SELECT {select_fields}
                FROM OPENQUERY(POSTGRES, 'SELECT {select_fields} 
                                          FROM {table_metadata.schema}.{source_table} 
                                          WHERE {daily_filter}')
            """
        else:
            # Conexão direta
            return f"""
                SELECT {select_fields}
                FROM {table_metadata.schema}.{source_table}
                WHERE {daily_filter}
            """
    
    def _build_daily_filter(self) -> str:
        """Constrói filtro para incremental diário (baseado nas DAGs V3)"""
        # ✅ Para OPENQUERY, sempre usar sintaxe PostgreSQL com aspas duplas
        return """(TIMESTAMP 'epoch' + dt_inc * INTERVAL '1 millisecond' >= DATE_TRUNC('day', CURRENT_TIMESTAMP)
                  OR
                  TIMESTAMP 'epoch' + dt_alt * INTERVAL '1 millisecond' >= DATE_TRUNC('day', CURRENT_TIMESTAMP))"""

    def _build_daily_filter_for_postgres_delete(self) -> str:
        """Constrói filtro para DELETE no PostgreSQL (sintaxe PostgreSQL pura)"""
        return """(TIMESTAMP 'epoch' + dt_inc * INTERVAL '1 millisecond' >= DATE_TRUNC('day', CURRENT_TIMESTAMP)
                  OR
                  TIMESTAMP 'epoch' + dt_alt * INTERVAL '1 millisecond' >= DATE_TRUNC('day', CURRENT_TIMESTAMP))"""
    
    def prepare_target_table(self, table_metadata: TableMetadata, 
                           sample_data=None) -> bool:
        """Prepara tabela para incremental (cria se não existir)"""
        try:
            full_table_name = self.get_full_table_name(table_metadata)
            
            # Verifica se tabela existe
            table_exists = self.target_connection.table_exists(
                table_metadata.target_schema,
                f"{table_metadata.table_prefix}{table_metadata.name}"
            )
            
            if not table_exists and sample_data is not None:
                # Cria tabela baseada no sample
                from ..processors.schema_manager import SchemaManager
                
                schema_manager = SchemaManager(self.target_connection)
                return schema_manager.create_table_from_dataframe(
                    table_metadata, sample_data
                )
            
            return True
            
        except Exception as e:
            self.logger.error(f"Erro ao preparar tabela para incremental: {str(e)}")
            return False
    
    def _execute_etl(self, table_metadata: TableMetadata, extract_query: str) -> int:
        """Executa ETL incremental diário"""
        from ..processors.data_processor import DataProcessor
        
        # Constrói condições de DELETE para reprocessamento
        delete_conditions = self._build_delete_conditions(table_metadata)
        
        processor = DataProcessor(
            source_connection=self.source_connection,
            target_connection=self.target_connection,
            chunk_size=self.chunk_size
        )
        
        return processor.transfer_table_data(
            extract_query=extract_query,
            table_metadata=table_metadata,
            is_full_load=False,
            delete_conditions=delete_conditions
        )
    
    def _build_delete_conditions(self, table_metadata: TableMetadata) -> list:
        """
        Constrói condições de DELETE inteligente para reprocessamento
        ✅ Implementa lógica do V3: DELETE baseado nos dados que serão inseridos
        """
        delete_conditions = []
        full_table_name = self.get_full_table_name(table_metadata)

        # ✅ ESTRATÉGIA INTELIGENTE: DELETE baseado nos dados que serão inseridos (como V3)
        if table_metadata.name == 'syo_encaminhamento':
            # ✅ Caso especial syo_encaminhamento: DELETE baseado nos id_evento que serão atualizados
            delete_conditions.extend(self._build_syo_encaminhamento_delete(table_metadata, full_table_name))

        elif table_metadata.id_field and ',' in table_metadata.id_field:
            # ✅ Chave composta: DELETE inteligente baseado nos IDs que serão inseridos
            delete_conditions.extend(self._build_composite_key_delete(table_metadata, full_table_name))

        elif table_metadata.id_field:
            # ✅ Chave simples: DELETE inteligente baseado nos IDs que serão inseridos
            delete_conditions.extend(self._build_simple_key_delete(table_metadata, full_table_name))

        else:
            # ✅ Sem ID field: DELETE baseado no filtro temporal (fallback)
            daily_filter = self._build_daily_filter_for_postgres_delete()
            delete_sql = f"""
                DELETE FROM {full_table_name}
                WHERE {daily_filter}
            """
            delete_conditions.append(delete_sql)

        return delete_conditions

    def _build_syo_encaminhamento_delete(self, table_metadata: TableMetadata, full_table_name: str) -> list:
        """
        ✅ DELETE inteligente para syo_encaminhamento (baseado no V3)
        Busca id_evento que serão atualizados e faz DELETE específico
        """
        delete_conditions = []

        try:
            self.logger.info(f"🗑️ Preparando DELETE inteligente para syo_encaminhamento")

            # Buscar id_evento que serão atualizados (baseado no filtro incremental)
            if hasattr(self.source_connection, 'execute_openquery'):
                # SQL Server com OPENQUERY
                check_query = f"""
                SELECT DISTINCT id_evento
                FROM OPENQUERY(POSTGRES, 'SELECT DISTINCT id_evento FROM PUBLIC.syo_encaminhamento
                                          WHERE (TIMESTAMP ''epoch'' + dt_inc * INTERVAL ''1 millisecond'' >= DATE_TRUNC(''day'', CURRENT_TIMESTAMP)
                                          OR TIMESTAMP ''epoch'' + dt_alt * INTERVAL ''1 millisecond'' >= DATE_TRUNC(''day'', CURRENT_TIMESTAMP))')
                """
            else:
                # Conexão direta
                daily_filter = self._build_daily_filter()
                check_query = f"""
                SELECT DISTINCT id_evento
                FROM {table_metadata.schema}.syo_encaminhamento
                WHERE {daily_filter}
                """

            # Executa query para buscar IDs
            with self.source_connection.get_cursor() as (cursor, connection):
                cursor.execute(check_query)
                results = cursor.fetchall()

                if results:
                    # Constrói lista de IDs para DELETE
                    ids_list = "','".join([str(row[0]) for row in results])
                    delete_sql = f"DELETE FROM {full_table_name} WHERE id_evento IN ('{ids_list}')"
                    delete_conditions.append(delete_sql)
                    self.logger.info(f"🗑️ DELETE preparado para {len(results)} eventos em syo_encaminhamento")
                else:
                    self.logger.info(f"🗑️ Nenhum evento para DELETE em syo_encaminhamento")

        except Exception as e:
            self.logger.warning(f"⚠️ Erro no DELETE inteligente para syo_encaminhamento: {e}")
            # Fallback para DELETE temporal
            daily_filter = self._build_daily_filter()
            delete_sql = f"DELETE FROM {full_table_name} WHERE {daily_filter}"
            delete_conditions.append(delete_sql)

        return delete_conditions

    def _build_composite_key_delete(self, table_metadata: TableMetadata, full_table_name: str) -> list:
        """
        ✅ DELETE inteligente para chaves compostas (baseado no V3)
        Busca combinações de chaves que serão atualizadas
        """
        delete_conditions = []

        try:
            id_fields = [field.strip() for field in table_metadata.id_field.split(',')]
            self.logger.info(f"🗑️ Preparando DELETE inteligente para chave composta: {id_fields}")

            # Construir concatenação para PostgreSQL (origem)
            pg_concat = "||'-'||".join([f"{field}::text" for field in id_fields])

            if hasattr(self.source_connection, 'execute_openquery'):
                # SQL Server com OPENQUERY
                dash_separator = " + '-' + "
                check_query = f"""
                SELECT {dash_separator.join([f"CAST({field} AS VARCHAR(50))" for field in id_fields])} as composite_key
                FROM OPENQUERY(POSTGRES, 'SELECT {table_metadata.id_field} FROM {table_metadata.schema}.{table_metadata.source_table or table_metadata.name} WHERE {self._build_daily_filter()}')
                """
            else:
                # Conexão direta
                daily_filter = self._build_daily_filter()
                check_query = f"""
                SELECT {pg_concat} as composite_key
                FROM {table_metadata.schema}.{table_metadata.source_table or table_metadata.name}
                WHERE {daily_filter}
                """

            # Executa query para buscar chaves compostas
            with self.source_connection.get_cursor() as (cursor, connection):
                cursor.execute(check_query)
                results = cursor.fetchall()

                if results:
                    # Constrói lista de chaves compostas para DELETE
                    keys_list = "','".join([str(row[0]) for row in results])
                    pg_concat_target = "||'-'||".join([f"{field}::text" for field in id_fields])
                    delete_sql = f"DELETE FROM {full_table_name} WHERE {pg_concat_target} IN ('{keys_list}')"
                    delete_conditions.append(delete_sql)
                    self.logger.info(f"🗑️ DELETE preparado para {len(results)} chaves compostas")
                else:
                    self.logger.info(f"🗑️ Nenhuma chave composta para DELETE")

        except Exception as e:
            self.logger.warning(f"⚠️ Erro no DELETE inteligente para chave composta: {e}")
            # Fallback para DELETE temporal
            daily_filter = self._build_daily_filter()
            delete_sql = f"DELETE FROM {full_table_name} WHERE {daily_filter}"
            delete_conditions.append(delete_sql)

        return delete_conditions

    def _build_simple_key_delete(self, table_metadata: TableMetadata, full_table_name: str) -> list:
        """
        ✅ DELETE inteligente para chaves simples (baseado no V3)
        Busca IDs que serão atualizados
        """
        delete_conditions = []

        try:
            self.logger.info(f"🗑️ Preparando DELETE inteligente para chave simples: {table_metadata.id_field}")

            if hasattr(self.source_connection, 'execute_openquery'):
                # SQL Server com OPENQUERY
                check_query = f"""
                SELECT {table_metadata.id_field}
                FROM OPENQUERY(POSTGRES, 'SELECT {table_metadata.id_field} FROM {table_metadata.schema}.{table_metadata.source_table or table_metadata.name} WHERE {self._build_daily_filter()}')
                """
            else:
                # Conexão direta
                daily_filter = self._build_daily_filter()
                check_query = f"""
                SELECT {table_metadata.id_field}
                FROM {table_metadata.schema}.{table_metadata.source_table or table_metadata.name}
                WHERE {daily_filter}
                """

            # Executa query para buscar IDs
            with self.source_connection.get_cursor() as (cursor, connection):
                cursor.execute(check_query)
                results = cursor.fetchall()

                if results:
                    # Constrói lista de IDs para DELETE
                    ids_list = "','".join([str(row[0]) for row in results])
                    delete_sql = f"DELETE FROM {full_table_name} WHERE {table_metadata.id_field} IN ('{ids_list}')"
                    delete_conditions.append(delete_sql)
                    self.logger.info(f"🗑️ DELETE preparado para {len(results)} IDs")
                else:
                    self.logger.info(f"🗑️ Nenhum ID para DELETE")

        except Exception as e:
            self.logger.warning(f"⚠️ Erro no DELETE inteligente para chave simples: {e}")
            # Fallback para DELETE temporal
            daily_filter = self._build_daily_filter()
            delete_sql = f"DELETE FROM {full_table_name} WHERE {daily_filter}"
            delete_conditions.append(delete_sql)

        return delete_conditions
