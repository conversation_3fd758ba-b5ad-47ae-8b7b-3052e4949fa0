"""
Estratégia Incremental Inteligente - ETL Framework

Implementa estratégia inteligente com fallback automático baseada na lógica das DAGs V3.
"""

from .base import ETLStrategy, ETLMode, TableMetadata, ETLResult
from .incremental_daily import IncrementalDailyStrategy
from .incremental_seven_days import IncrementalSevenDaysStrategy
from .full_load import FullLoadStrategy
from typing import Dict, Any, Optional
import time


class SmartIncrementalStrategy(ETLStrategy):
    """
    Estratégia Incremental Inteligente
    
    Implementa a lógica de 3 níveis das DAGs V3:
    1. Tenta Incremental Diário
    2. Se falhar, analisa diferença e decide entre 7 dias ou full load
    3. Se 7 dias falhar (timeout), faz full load
    
    Características:
    - Análise automática de diferenças
    - Fallback inteligente baseado em percentual
    - Controle de timeout
    - Logs detalhados de decisões
    """
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # Inicializa estratégias filhas
        self.daily_strategy = IncrementalDailyStrategy(*args, **kwargs)
        self.seven_days_strategy = IncrementalSevenDaysStrategy(*args, **kwargs)
        self.full_load_strategy = FullLoadStrategy(*args, **kwargs)
    
    def get_mode(self) -> ETLMode:
        return ETLMode.SMART_INCREMENTAL
    
    def should_use_strategy(self, table_metadata: TableMetadata, 
                          analysis_info: Optional[Dict[str, Any]] = None) -> bool:
        """Smart incremental pode ser usado para qualquer tabela com ID"""
        return bool(table_metadata.id_field)
    
    def build_extract_query(self, table_metadata: TableMetadata) -> str:
        """Não usado diretamente - cada estratégia filha constrói sua própria query"""
        return ""
    
    def prepare_target_table(self, table_metadata: TableMetadata, 
                           sample_data=None) -> bool:
        """Preparação será feita pela estratégia escolhida"""
        return True
    
    def execute(self, table_metadata: TableMetadata) -> ETLResult:
        """
        Executa estratégia inteligente com fallback automático
        
        Fluxo baseado nas DAGs V3:
        1. Incremental Diário
        2. Análise de diferença
        3. Incremental 7 Dias OU Full Load (baseado na análise)
        4. Full Load (se 7 dias falhar)
        """
        start_time = time.time()
        
        self.logger.info(f"🚀 Iniciando estratégia inteligente para {table_metadata.name}")
        
        # NÍVEL 1: Tenta Incremental Diário
        daily_result = self._try_incremental_daily(table_metadata)
        if daily_result.success:
            self.logger.info(f"✅ Incremental diário bem-sucedido para {table_metadata.name}")
            return daily_result
        
        self.logger.warning(f"⚠️ Incremental diário falhou para {table_metadata.name}: {daily_result.error_message}")
        
        # NÍVEL 2: Análise de diferença para decidir próximo passo
        analysis_info = self._analyze_data_gap(table_metadata)
        
        should_use_seven_days = analysis_info.get('should_use_seven_days', False)
        diff_percentage = analysis_info.get('diff_percentage', 0)
        
        self.logger.info(f"📊 Análise: diferença={diff_percentage:.1f}%, usar_7_dias={should_use_seven_days}")
        
        if should_use_seven_days:
            # NÍVEL 3A: Tenta Incremental 7 Dias
            seven_days_result = self._try_incremental_seven_days(table_metadata, analysis_info)
            if seven_days_result.success:
                self.logger.info(f"✅ Incremental 7 dias bem-sucedido para {table_metadata.name}")
                seven_days_result.fallback_used = True
                return seven_days_result
            
            self.logger.warning(f"⚠️ Incremental 7 dias falhou para {table_metadata.name}: {seven_days_result.error_message}")
        
        # NÍVEL 3B: Full Load como último recurso
        self.logger.info(f"🔄 Executando full load como fallback final para {table_metadata.name}")
        full_load_result = self._try_full_load(table_metadata)
        full_load_result.fallback_used = True
        
        if full_load_result.success:
            self.logger.info(f"✅ Full load bem-sucedido para {table_metadata.name}")
        else:
            self.logger.error(f"❌ Full load falhou para {table_metadata.name}: {full_load_result.error_message}")
        
        return full_load_result
    
    def _try_incremental_daily(self, table_metadata: TableMetadata) -> ETLResult:
        """Tenta estratégia incremental diária"""
        try:
            return self.daily_strategy.execute(table_metadata)
        except Exception as e:
            return ETLResult(
                success=False,
                mode_used=ETLMode.INCREMENTAL_DAILY,
                records_processed=0,
                execution_time=0,
                source_count=0,
                dest_count=0,
                error_message=str(e)
            )
    
    def _try_incremental_seven_days(self, table_metadata: TableMetadata, 
                                  analysis_info: Dict[str, Any]) -> ETLResult:
        """Tenta estratégia incremental 7 dias"""
        try:
            return self.seven_days_strategy.execute(table_metadata)
        except TimeoutError as e:
            self.logger.warning(f"⏱️ Timeout em incremental 7 dias: {str(e)}")
            return ETLResult(
                success=False,
                mode_used=ETLMode.INCREMENTAL_SEVEN_DAYS,
                records_processed=0,
                execution_time=self.timeout_seconds,
                source_count=0,
                dest_count=0,
                error_message=f"Timeout: {str(e)}"
            )
        except Exception as e:
            return ETLResult(
                success=False,
                mode_used=ETLMode.INCREMENTAL_SEVEN_DAYS,
                records_processed=0,
                execution_time=0,
                source_count=0,
                dest_count=0,
                error_message=str(e)
            )
    
    def _try_full_load(self, table_metadata: TableMetadata) -> ETLResult:
        """Tenta estratégia full load"""
        try:
            return self.full_load_strategy.execute(table_metadata)
        except Exception as e:
            return ETLResult(
                success=False,
                mode_used=ETLMode.FULL_LOAD,
                records_processed=0,
                execution_time=0,
                source_count=0,
                dest_count=0,
                error_message=str(e)
            )
    
    def _analyze_data_gap(self, table_metadata: TableMetadata) -> Dict[str, Any]:
        """
        Analisa diferença entre origem e destino para decidir estratégia
        Baseado na lógica validate_single_table das DAGs V3
        """
        try:
            # Conta registros no destino
            dest_count = self.target_connection.get_table_count(
                table_metadata.target_schema,
                f"{table_metadata.table_prefix}{table_metadata.name}"
            )
            
            # Conta registros na origem
            source_table = table_metadata.source_table or table_metadata.name
            source_count = self.source_connection.get_table_count(
                table_metadata.schema,
                source_table
            )
            
            # Calcula diferenças
            abs_diff = abs(dest_count - source_count)
            diff_percentage = (abs_diff / source_count * 100) if source_count > 0 else 0
            
            # Decisão baseada na lógica das DAGs V3
            should_use_seven_days = (
                source_count > 0 and
                dest_count < source_count and
                diff_percentage <= self.max_gap_percentage
            )
            
            self.logger.info(f"📊 Análise gap: origem={source_count:,}, destino={dest_count:,}, diff={diff_percentage:.1f}%")
            
            return {
                'source_count': source_count,
                'dest_count': dest_count,
                'abs_diff': abs_diff,
                'diff_percentage': diff_percentage,
                'should_use_seven_days': should_use_seven_days
            }
            
        except Exception as e:
            self.logger.error(f"Erro na análise de gap: {str(e)}")
            return {
                'source_count': 0,
                'dest_count': 0,
                'abs_diff': 0,
                'diff_percentage': 100,
                'should_use_seven_days': False
            }
    
    def _execute_etl(self, table_metadata: TableMetadata, extract_query: str) -> int:
        """Não usado - execute() implementa lógica própria"""
        return 0
