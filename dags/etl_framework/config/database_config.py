"""
Configuração de Banco de Dados - ETL Framework

Configurações de conexão para diferentes SGBDs.
"""

from dataclasses import dataclass
from typing import Dict, Any, Optional, List
from enum import Enum


class DatabaseType(Enum):
    """Tipos de banco de dados suportados"""
    POSTGRESQL = "postgresql"
    SQLSERVER = "sqlserver"
    ORACLE = "oracle"
    MYSQL = "mysql"


@dataclass
class DatabaseConfig:
    """
    Configuração de conexão com banco de dados
    
    Unifica configurações para diferentes SGBDs
    """
    name: str
    db_type: DatabaseType
    host: str
    port: int
    database: str
    user: str
    password: str
    
    # Configurações de conexão
    timeout: int = 300
    pool_size: int = 15
    max_overflow: int = 10
    
    # Configurações específicas
    schema: str = "public"
    extra_params: Optional[Dict[str, Any]] = None
    
    # Configurações SSL
    ssl_mode: Optional[str] = None
    ssl_cert: Optional[str] = None
    ssl_key: Optional[str] = None
    ssl_ca: Optional[str] = None
    
    def __post_init__(self):
        """Validações e configurações automáticas"""
        # Configurações padrão por tipo de banco
        if self.db_type == DatabaseType.POSTGRESQL and self.port == 0:
            self.port = 5432
        elif self.db_type == DatabaseType.SQLSERVER and self.port == 0:
            self.port = 1433
        elif self.db_type == DatabaseType.ORACLE and self.port == 0:
            self.port = 1521
        elif self.db_type == DatabaseType.MYSQL and self.port == 0:
            self.port = 3306
    
    def get_connection_string(self) -> str:
        """Gera string de conexão baseada no tipo de banco"""
        if self.db_type == DatabaseType.POSTGRESQL:
            return self._get_postgresql_connection_string()
        elif self.db_type == DatabaseType.SQLSERVER:
            return self._get_sqlserver_connection_string()
        elif self.db_type == DatabaseType.ORACLE:
            return self._get_oracle_connection_string()
        elif self.db_type == DatabaseType.MYSQL:
            return self._get_mysql_connection_string()
        else:
            raise ValueError(f"Tipo de banco não suportado: {self.db_type}")
    
    def _get_postgresql_connection_string(self) -> str:
        """String de conexão PostgreSQL"""
        conn_str = (
            f"host={self.host} "
            f"port={self.port} "
            f"database={self.database} "
            f"user={self.user} "
            f"password={self.password}"
        )
        
        if self.ssl_mode:
            conn_str += f" sslmode={self.ssl_mode}"
        
        if self.extra_params:
            for key, value in self.extra_params.items():
                conn_str += f" {key}={value}"
        
        return conn_str
    
    def _get_sqlserver_connection_string(self) -> str:
        """String de conexão SQL Server"""
        conn_str = (
            f"server={self.host};"
            f"port={self.port};"
            f"database={self.database};"
            f"user={self.user};"
            f"password={self.password};"
            f"timeout={self.timeout}"
        )
        
        if self.extra_params:
            for key, value in self.extra_params.items():
                conn_str += f";{key}={value}"
        
        return conn_str
    
    def _get_oracle_connection_string(self) -> str:
        """String de conexão Oracle"""
        if self.extra_params and 'service_name' in self.extra_params:
            # Usando service name
            return f"{self.user}/{self.password}@{self.host}:{self.port}/{self.extra_params['service_name']}"
        else:
            # Usando SID
            return f"{self.user}/{self.password}@{self.host}:{self.port}/{self.database}"
    
    def _get_mysql_connection_string(self) -> str:
        """String de conexão MySQL"""
        conn_str = (
            f"host={self.host};"
            f"port={self.port};"
            f"database={self.database};"
            f"user={self.user};"
            f"password={self.password}"
        )
        
        if self.ssl_mode:
            conn_str += f";ssl_mode={self.ssl_mode}"
        
        if self.extra_params:
            for key, value in self.extra_params.items():
                conn_str += f";{key}={value}"
        
        return conn_str
    
    def get_sqlalchemy_url(self) -> str:
        """Gera URL SQLAlchemy"""
        if self.db_type == DatabaseType.POSTGRESQL:
            return f"postgresql://{self.user}:{self.password}@{self.host}:{self.port}/{self.database}"
        elif self.db_type == DatabaseType.SQLSERVER:
            return f"mssql+pymssql://{self.user}:{self.password}@{self.host}:{self.port}/{self.database}"
        elif self.db_type == DatabaseType.ORACLE:
            return f"oracle+cx_oracle://{self.user}:{self.password}@{self.host}:{self.port}/{self.database}"
        elif self.db_type == DatabaseType.MYSQL:
            return f"mysql+pymysql://{self.user}:{self.password}@{self.host}:{self.port}/{self.database}"
        else:
            raise ValueError(f"Tipo de banco não suportado: {self.db_type}")
    
    def validate(self) -> List[str]:
        """Valida configuração"""
        errors = []
        
        if not self.name:
            errors.append("Nome da configuração não definido")
        
        if not self.host:
            errors.append("Host não definido")
        
        if not self.database:
            errors.append("Database não definido")
        
        if not self.user:
            errors.append("Usuário não definido")
        
        if not self.password:
            errors.append("Senha não definida")
        
        if self.port <= 0:
            errors.append("Porta inválida")
        
        return errors
    
    def to_dict(self) -> Dict[str, Any]:
        """Converte para dicionário"""
        return {
            'name': self.name,
            'db_type': self.db_type.value,
            'host': self.host,
            'port': self.port,
            'database': self.database,
            'user': self.user,
            'password': self.password,
            'timeout': self.timeout,
            'pool_size': self.pool_size,
            'max_overflow': self.max_overflow,
            'schema': self.schema,
            'extra_params': self.extra_params,
            'ssl_mode': self.ssl_mode,
            'ssl_cert': self.ssl_cert,
            'ssl_key': self.ssl_key,
            'ssl_ca': self.ssl_ca
        }
    
    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> 'DatabaseConfig':
        """Cria DatabaseConfig a partir de dicionário"""
        return cls(
            name=config_dict['name'],
            db_type=DatabaseType(config_dict['db_type']),
            host=config_dict['host'],
            port=config_dict['port'],
            database=config_dict['database'],
            user=config_dict['user'],
            password=config_dict['password'],
            timeout=config_dict.get('timeout', 300),
            pool_size=config_dict.get('pool_size', 15),
            max_overflow=config_dict.get('max_overflow', 10),
            schema=config_dict.get('schema', 'public'),
            extra_params=config_dict.get('extra_params'),
            ssl_mode=config_dict.get('ssl_mode'),
            ssl_cert=config_dict.get('ssl_cert'),
            ssl_key=config_dict.get('ssl_key'),
            ssl_ca=config_dict.get('ssl_ca')
        )
    
    @classmethod
    def from_legacy_dict(cls, name: str, legacy_dict: Dict[str, Any], db_type: DatabaseType) -> 'DatabaseConfig':
        """
        Cria DatabaseConfig a partir de dicionário legado (compatibilidade com DAGs V3)
        """
        # Mapeia campos legados para novos
        host = legacy_dict.get('host', legacy_dict.get('server'))
        
        return cls(
            name=name,
            db_type=db_type,
            host=host,
            port=legacy_dict.get('port', 0),
            database=legacy_dict.get('database'),
            user=legacy_dict.get('user'),
            password=legacy_dict.get('password'),
            timeout=legacy_dict.get('timeout', 300),
            pool_size=legacy_dict.get('pool_size', 15),
            extra_params=legacy_dict.get('extra_params')
        )
