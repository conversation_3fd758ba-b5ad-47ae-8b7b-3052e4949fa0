"""
Configuração do Data Warehouse Corporativo - ETL Framework

Configuração única do DW que será usada por todos os sistemas.
"""

from dataclasses import dataclass
from typing import Dict, Any
from .database_config import DatabaseConfig, DatabaseType


@dataclass
class DWConfig:
    """
    Configuração única do Data Warehouse Corporativo
    
    Todos os sistemas usam o mesmo DW como destino, apenas os schemas mudam
    """
    
    # Configuração da conexão (única para toda organização)
    connection: DatabaseConfig
    
    # Schemas por camada
    bronze_schema: str = "bronze"
    silver_schema: str = "silver"
    gold_schema: str = "gold"
    
    # Schema de metadados e controle
    metadata_schema: str = "metadata"
    permissions_table: str = "aux_permissions_tables"
    
    # Configurações padrão
    default_chunk_size: int = 50000
    default_timeout: int = 300
    max_retries: int = 2
    
    def get_bronze_schema_for_system(self, system_name: str) -> str:
        """Retorna schema bronze específico do sistema"""
        return f"{self.bronze_schema}_{system_name.lower()}"
    
    def get_silver_schema_for_system(self, system_name: str) -> str:
        """Retorna schema silver específico do sistema"""
        return f"{self.silver_schema}_{system_name.lower()}"
    
    def get_table_name(self, system_name: str, table_name: str, layer: str = "bronze") -> str:
        """Gera nome completo da tabela no DW"""
        if layer == "bronze":
            schema = self.get_bronze_schema_for_system(system_name)
            return f"{schema}.{table_name}"
        elif layer == "silver":
            schema = self.get_silver_schema_for_system(system_name)
            return f"{schema}.{table_name}"
        else:
            raise ValueError(f"Layer não suportada: {layer}")
    
    def get_permissions_table_path(self) -> str:
        """Retorna caminho completo da tabela de permissões"""
        return f"{self.metadata_schema}.{self.permissions_table}"
    
    def to_dict(self) -> Dict[str, Any]:
        """Converte para dicionário"""
        return {
            'connection': self.connection.to_dict(),
            'bronze_schema': self.bronze_schema,
            'silver_schema': self.silver_schema,
            'gold_schema': self.gold_schema,
            'metadata_schema': self.metadata_schema,
            'permissions_table': self.permissions_table,
            'default_chunk_size': self.default_chunk_size,
            'default_timeout': self.default_timeout,
            'max_retries': self.max_retries
        }
    
    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> 'DWConfig':
        """Cria DWConfig a partir de dicionário"""
        return cls(
            connection=DatabaseConfig.from_dict(config_dict['connection']),
            bronze_schema=config_dict.get('bronze_schema', 'bronze'),
            silver_schema=config_dict.get('silver_schema', 'silver'),
            gold_schema=config_dict.get('gold_schema', 'gold'),
            metadata_schema=config_dict.get('metadata_schema', 'metadata'),
            permissions_table=config_dict.get('permissions_table', 'aux_permissions_tables'),
            default_chunk_size=config_dict.get('default_chunk_size', 50000),
            default_timeout=config_dict.get('default_timeout', 300),
            max_retries=config_dict.get('max_retries', 2)
        )


def create_corporate_dw_config() -> DWConfig:
    """
    Cria configuração padrão do DW Corporativo
    Baseada nas configurações das DAGs V3
    """
    
    # Configuração única do DW Corporativo
    dw_connection = DatabaseConfig(
        name="dw_corporativo",
        db_type=DatabaseType.POSTGRESQL,
        host="***********",
        port=5432,
        database="postgres",
        user="bq_dwcorporativo_u",
        password="N#OK+#{Yx*",
        timeout=300,
        pool_size=20,  # Pool maior para DW
        schema="dbdwcorporativo"
    )
    
    return DWConfig(
        connection=dw_connection,
        bronze_schema="dbdwcorporativo",  # Schema atual das DAGs V3
        silver_schema="dbdwcorporativo",  # Mesmo schema para silver
        gold_schema="dbdwcorporativo",    # Futuro uso
        metadata_schema="dbdwcorporativo",
        permissions_table="aux_permissions_tables",
        default_chunk_size=50000,
        default_timeout=300,
        max_retries=2
    )
