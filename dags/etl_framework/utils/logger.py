"""
Sistema de Logging - ETL Framework

Sistema de logging estruturado para ETL baseado na lógica das DAGs V3.
"""

import logging
import time
from typing import Dict, Any, Optional
from datetime import datetime
from contextlib import contextmanager


class ETLLogger:
    """
    Logger estruturado para ETL
    
    Implementa:
    - Logs estruturados com contexto
    - Métricas de performance
    - Logs de progresso
    - Formatação consistente
    """
    
    def __init__(self, name: str, level: int = logging.INFO):
        self.logger = logging.getLogger(name)
        self.logger.setLevel(level)
        
        # Configura handler se não existir
        if not self.logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
        
        self.start_times = {}
        self.metrics = {}
    
    def info(self, message: str, **kwargs):
        """Log de informação com contexto"""
        formatted_message = self._format_message(message, **kwargs)
        self.logger.info(formatted_message)
    
    def warning(self, message: str, **kwargs):
        """Log de warning com contexto"""
        formatted_message = self._format_message(message, **kwargs)
        self.logger.warning(formatted_message)
    
    def error(self, message: str, **kwargs):
        """Log de erro com contexto"""
        formatted_message = self._format_message(message, **kwargs)
        self.logger.error(formatted_message)
    
    def debug(self, message: str, **kwargs):
        """Log de debug com contexto"""
        formatted_message = self._format_message(message, **kwargs)
        self.logger.debug(formatted_message)
    
    def _format_message(self, message: str, **kwargs) -> str:
        """Formata mensagem com contexto adicional"""
        if kwargs:
            context_parts = []
            for key, value in kwargs.items():
                context_parts.append(f"{key}={value}")
            context = " | ".join(context_parts)
            return f"{message} | {context}"
        return message
    
    def start_timer(self, operation: str):
        """Inicia timer para operação"""
        self.start_times[operation] = time.time()
        self.info(f"⏱️ Iniciando {operation}")
    
    def end_timer(self, operation: str, **kwargs) -> float:
        """Finaliza timer e retorna tempo decorrido"""
        if operation not in self.start_times:
            self.warning(f"Timer não encontrado para operação: {operation}")
            return 0.0
        
        elapsed_time = time.time() - self.start_times[operation]
        del self.start_times[operation]
        
        self.info(f"✅ {operation} concluído em {elapsed_time:.1f}s", **kwargs)
        return elapsed_time
    
    def log_progress(self, current: int, total: int, operation: str = "Processamento"):
        """Log de progresso"""
        percentage = (current / total * 100) if total > 0 else 0
        self.info(f"📊 {operation}: {current}/{total} ({percentage:.1f}%)")
    
    def log_table_start(self, table_name: str, strategy: str):
        """Log de início de processamento de tabela"""
        self.info(f"🚀 Iniciando ETL", table=table_name, strategy=strategy)
        self.start_timer(f"table_{table_name}")
    
    def log_table_success(self, table_name: str, records: int, **kwargs):
        """Log de sucesso no processamento de tabela"""
        elapsed_time = self.end_timer(f"table_{table_name}")
        self.info(f"✅ ETL concluído", 
                 table=table_name, 
                 records=records, 
                 time=f"{elapsed_time:.1f}s",
                 **kwargs)
    
    def log_table_error(self, table_name: str, error: str, **kwargs):
        """Log de erro no processamento de tabela"""
        elapsed_time = self.end_timer(f"table_{table_name}")
        self.error(f"❌ ETL falhou", 
                  table=table_name, 
                  error=error, 
                  time=f"{elapsed_time:.1f}s",
                  **kwargs)
    
    def log_validation(self, table_name: str, source_count: int, dest_count: int, is_valid: bool):
        """Log de validação"""
        status = "✅ Válido" if is_valid else "❌ Inválido"
        diff = abs(source_count - dest_count)
        diff_pct = (diff / source_count * 100) if source_count > 0 else 0
        
        self.info(f"🔍 Validação {status}", 
                 table=table_name,
                 source=source_count,
                 dest=dest_count,
                 diff=diff,
                 diff_pct=f"{diff_pct:.1f}%")
    
    def log_fallback(self, table_name: str, from_strategy: str, to_strategy: str, reason: str):
        """Log de fallback de estratégia"""
        self.warning(f"🔄 Fallback ativado", 
                    table=table_name,
                    from_strategy=from_strategy,
                    to_strategy=to_strategy,
                    reason=reason)
    
    def log_chunk_progress(self, chunk_num: int, chunk_size: int, total_processed: int):
        """Log de progresso de chunks"""
        self.info(f"📦 Chunk processado", 
                 chunk=chunk_num,
                 size=chunk_size,
                 total=total_processed)
    
    def log_performance_metrics(self, operation: str, metrics: Dict[str, Any]):
        """Log de métricas de performance"""
        self.info(f"📊 Métricas de performance", operation=operation, **metrics)
    
    @contextmanager
    def operation_context(self, operation: str, **context):
        """Context manager para operações com log automático"""
        self.start_timer(operation)
        try:
            self.info(f"🚀 Iniciando {operation}", **context)
            yield
            elapsed_time = self.end_timer(operation)
            self.info(f"✅ {operation} concluído", time=f"{elapsed_time:.1f}s", **context)
        except Exception as e:
            elapsed_time = self.end_timer(operation)
            self.error(f"❌ {operation} falhou", 
                      error=str(e), 
                      time=f"{elapsed_time:.1f}s", 
                      **context)
            raise
    
    def add_metric(self, key: str, value: Any):
        """Adiciona métrica para tracking"""
        self.metrics[key] = value
    
    def get_metrics(self) -> Dict[str, Any]:
        """Retorna métricas coletadas"""
        return self.metrics.copy()
    
    def clear_metrics(self):
        """Limpa métricas"""
        self.metrics.clear()
    
    def log_dag_start(self, dag_name: str, **context):
        """Log de início de DAG"""
        self.info(f"🚀 DAG iniciada", dag=dag_name, **context)
        self.start_timer(f"dag_{dag_name}")
    
    def log_dag_complete(self, dag_name: str, tables_processed: int, **context):
        """Log de conclusão de DAG"""
        elapsed_time = self.end_timer(f"dag_{dag_name}")
        self.info(f"✅ DAG concluída", 
                 dag=dag_name,
                 tables=tables_processed,
                 time=f"{elapsed_time:.1f}s",
                 **context)
    
    def log_dag_error(self, dag_name: str, error: str, **context):
        """Log de erro em DAG"""
        elapsed_time = self.end_timer(f"dag_{dag_name}")
        self.error(f"❌ DAG falhou", 
                  dag=dag_name,
                  error=error,
                  time=f"{elapsed_time:.1f}s",
                  **context)


def setup_logger(name: str, level: int = logging.INFO) -> ETLLogger:
    """Função utilitária para criar logger"""
    return ETLLogger(name, level)
