"""
Configuração de Tabelas SYONET - Migrada do V3
✅ Migração completa do TABLES_CONFIG das DAGs V3 para o formato V4

Mantém 100% de compatibilidade com a lógica original do V3_BRONZE_SYONET.py
"""

from etl_framework.config.table_config import TableConfig, TableType, IncrementalMode

# ✅ Filtros baseados exatamente no V3
DAILY_FILTER_CONDITION = """(TIMESTAMP 'epoch' + dt_inc * INTERVAL '1 millisecond' >= DATE_TRUNC('day', CURRENT_TIMESTAMP)
                             OR
                             TIMESTAMP 'epoch' + dt_alt * INTERVAL '1 millisecond' >= DATE_TRUNC('day', CURRENT_TIMESTAMP))"""

SEVEN_DAYS_FILTER_CONDITION = """(TIMESTAMP 'epoch' + dt_inc * INTERVAL '1 millisecond' >= CURRENT_TIMESTAMP - INTERVAL '7 days'
                                  OR
                                  TIMESTAMP 'epoch' + dt_alt * INTERVAL '1 millisecond' >= CURRENT_TIMESTAMP - INTERVAL '7 days')"""

def get_syonet_tables_config():
    """
    ✅ Configuração completa das tabelas SYONET migrada do V3
    Mantém exatamente a mesma lógica e casos especiais do V3_BRONZE_SYONET.py
    """
    
    tables = []
    
    # ===== TABELAS PEQUENAS (sempre full load) =====
    small_tables = [
        'syo_agenda', 'syo_contaemail', 'syo_contatoemail', 'syo_clientearea', 
        'syo_telefones', 'syo_novaclassificacao', 'syo_novaclassificacaotipocliente',
        'syo_faixanovaclassificacao', 'syo_etapafunil', 'syo_empresa', 'syo_empresausuario',
        'syo_donoconta', 'syo_oficina', 'syo_veiculo', 'syo_dadosinterfacecliente',
        'syo_interfacenegociacao', 'syo_campointerfacenegociacao', 'syo_modeloversao',
        'syo_motivoresultado'
    ]
    
    for table_name in small_tables:
        tables.append(TableConfig(
            name=table_name,
            table_type=TableType.SMALL,
            incremental_mode=IncrementalMode.FULL_ONLY,
            id_field=None,
            source_schema="PUBLIC",
            target_schema="dbdwcorporativo",
            table_prefix="bronze_syonet_",
            max_gap_percentage=10.0,
            max_abs_diff=10,
            timeout_seconds=300
        ))
    
    # ===== TABELAS COM CONFIGURAÇÕES ESPECIAIS =====
    
    # campanhav2whatsapp (source diferente)
    tables.append(TableConfig(
        name='campanhav2whatsapp',
        table_type=TableType.SMALL,
        incremental_mode=IncrementalMode.FULL_ONLY,
        source_table='syo_campanhav2whatsapp',
        source_schema="PUBLIC",
        target_schema="dbdwcorporativo",
        table_prefix="bronze_syonet_"
    ))

    # syo_usuario (select customizado)
    tables.append(TableConfig(
        name='syo_usuario',
        table_type=TableType.SMALL,
        incremental_mode=IncrementalMode.FULL_ONLY,
        select_fields='id_usuario,nm_login,id_grupo,id_dominio,id_tipo,ap_usuario,nm_usuario,nm_sobrenome,ds_usuario,ds_email,ic_ativo,id_empresa,no_cgccpf',
        source_schema="PUBLIC",
        target_schema="dbdwcorporativo",
        table_prefix="bronze_syonet_"
    ))
    
    # ===== TABELAS GRANDES (incremental com fallback) =====
    
    # syo_evento (select customizado complexo)
    tables.append(TableConfig(
        name='syo_evento',
        table_type=TableType.LARGE,
        incremental_mode=IncrementalMode.SMART,
        id_field='id_evento',
        select_fields='id_evento, id_pesquisa, id_contato, id_estacao, id_empresa, id_cliente, nm_cliente, id_agente, id_ramal, id_statusevento, id_situacaoevento, id_prioridadeevento, id_grupoevento, id_tipoevento, id_componente, id_grupocampanha, id_email, ds_formacontato, ds_acaoevento, dt_proximaacao, ds_iniciativacontato, ds_assunto, ds_origem, dt_horainicio, dt_horafinal, dt_limite, substring(ds_conclusao,1,3800) as ds_conclusao, dt_conclusao, ds_palavrachave, dt_previsaoresposta, dt_previsaotermino, ic_primeiroatendimento, id_dealer, id_campanha, no_versao, cd_usuarioinc, cd_usuarioalt, dt_inc, dt_alt, no_nota, ds_observacao_json, ds_prisma, ds_midia, ds_pedidocompra_json, id_agenda, no_minuta, ds_valorinvestido, ds_tempoinvestido, no_horaproximaacao, id_equipesobdemanda, no_pedido, dt_periodobase, id_modulocriacaoevento, ic_geradoautomatico, ic_principal, id_eventoprincipal, ic_tempolimiteesgotado, ds_resultado, ic_reativar, dt_classificacaofrio, id_campanhav2, ds_temperatura, uuid_evento, dt_visita, dt_venda, id_agendamentoerp, ic_centralleads, dt_previsaoentrega, id_filarecepcao, ds_resumolead, dt_visitafrotista, id_atendente_atual, id_empresa_atual, id_usuario_atual, no_os',
        source_schema="PUBLIC",
        target_schema="dbdwcorporativo",
        table_prefix="bronze_syonet_"
    ))

    # syo_cliente
    tables.append(TableConfig(
        name='syo_cliente',
        table_type=TableType.LARGE,
        incremental_mode=IncrementalMode.SMART,
        id_field='id_cliente',
        source_schema="PUBLIC",
        target_schema="dbdwcorporativo",
        table_prefix="bronze_syonet_"
    ))

    # syo_acao (select customizado)
    tables.append(TableConfig(
        name='syo_acao',
        table_type=TableType.LARGE,
        incremental_mode=IncrementalMode.SMART,
        id_field='id_acao',
        select_fields='id_acao, id_evento, id_cliente, id_agente, tp_acao, ds_origem, ds_iniciativa, dh_acao, ds_resultado, CAST(LEFT(ds_conclusaoacao, 1000) AS VARCHAR(3900)) as ds_conclusaoacao, id_motivoresultado, ds_descricao, ic_confirmado, ds_temperatura, id_contato, ds_pendencia, no_versao, cd_usuarioinc, cd_usuarioalt, dt_inc, dt_alt, ds_agendamento, ds_respostasmsmo, id_email, id_sms, no_latitude, no_longitude, ic_mobile',
        source_schema="PUBLIC",
        target_schema="dbdwcorporativo",
        table_prefix="bronze_syonet_"
    ))
    
    # Outras tabelas grandes simples
    large_tables_simple = [
        ('syo_cidade', 'id_cidade'),
        ('syo_clientealteracao', 'id_clientealteracao'),
        ('syo_registrointerface', 'id_registrointerface'),
        ('syo_clientenovaclassificacao', 'id_novaclassificacao'),
        ('syo_historicoetapafunilevento', 'id_historicoetapafunilevento'),
        ('syo_donocontacliente', 'id_donocontacliente'),
        ('syo_peca', 'no_controle')
    ]
    
    for table_name, id_field in large_tables_simple:
        tables.append(TableConfig(
            name=table_name,
            table_type=TableType.LARGE,
            incremental_mode=IncrementalMode.SMART,
            id_field=id_field,
            source_schema="PUBLIC",
            target_schema="dbdwcorporativo",
            table_prefix="bronze_syonet_"
        ))
    
    # ===== TABELAS COM SQL CUSTOMIZADO (casos especiais do V3) =====

    # syo_encaminhamento (chave composta + SQL customizado complexo)
    tables.append(TableConfig(
        name='syo_encaminhamento',
        table_type=TableType.CUSTOM,
        incremental_mode=IncrementalMode.SMART,
        id_field='id_encaminhamento,id_evento',  # Chave composta
        source_schema="PUBLIC",
        target_schema="dbdwcorporativo",
        table_prefix="bronze_syonet_",
        custom_sql={
            'incremental': f"""
                SELECT *
                FROM OPENQUERY(POSTGRES,
                '
                with
                base as (
                    select distinct id_evento from public.syo_encaminhamento
                    where (TIMESTAMP ''epoch'' + dt_inc * INTERVAL ''1 millisecond'' >= DATE_TRUNC(''day'', CURRENT_TIMESTAMP)
                    OR
                    TIMESTAMP ''epoch'' + dt_alt * INTERVAL ''1 millisecond'' >= DATE_TRUNC(''day'', CURRENT_TIMESTAMP))
                )
                select e.* from public.syo_encaminhamento e
                inner join base b on e.id_evento = b.id_evento
                ')
            """
        }
    ))

    # syo_empresacliente (chave composta + SQL customizado)
    tables.append(TableConfig(
        name='syo_empresacliente',
        table_type=TableType.CUSTOM,
        incremental_mode=IncrementalMode.SMART,
        id_field='id_cliente,id_empresa',  # Chave composta
        source_schema="PUBLIC",
        target_schema="dbdwcorporativo",
        table_prefix="bronze_syonet_",
        custom_sql={
            'incremental': f"""
                SELECT *
                FROM OPENQUERY(POSTGRES, 'SELECT *
                                            FROM PUBLIC.syo_empresacliente
                                            WHERE (dt_inc is not null  OR dt_alt is not null)
                                                AND {DAILY_FILTER_CONDITION}')
            """
        }
    ))

    # syo_camposregistrointerface (select customizado)
    tables.append(TableConfig(
        name='syo_camposregistrointerface',
        table_type=TableType.LARGE,
        incremental_mode=IncrementalMode.SMART,
        id_field='id_camposregistrointerface',
        select_fields='id_camposregistrointerface, id_campointerfacenegociacao, id_registrointerface, ds_etiqueta, CAST(LEFT(ds_valor, 1000) AS VARCHAR(3900)) as ds_valor, no_versao, cd_usuarioinc, cd_usuarioalt, dt_inc, dt_alt',
        source_schema="PUBLIC",
        target_schema="dbdwcorporativo",
        table_prefix="bronze_syonet_"
    ))

    # syo_evento_obs (SQL customizado complexo)
    tables.append(TableConfig(
        name='syo_evento_obs',
        table_type=TableType.CUSTOM,
        incremental_mode=IncrementalMode.SMART,
        id_field='id_evento',
        source_table='syo_evento',
        source_schema="PUBLIC",
        target_schema="dbdwcorporativo",
        table_prefix="bronze_syonet_",
        custom_sql={
            'full': """
                SELECT id_evento, dt_inc, ds_observacao
                FROM OPENQUERY(POSTGRES, 'SELECT id_evento, dt_inc, ds_observacao
                                          FROM public.syo_evento
                                          WHERE ds_observacao LIKE ''%Cadencia Meetime:%''')
            """
        }
    ))

    return tables
