"""
Configuração do Sistema Syonet - ETL Framework

Configurações específicas do Syonet baseadas nas DAGs V3.
Refatorado para nova arquitetura: origem única (SQL Server), destino ú<PERSON> (DW).
"""

from ...config.system_config import SystemConfig
from ...config.table_config import TableConfig, TableType, IncrementalMode
from ...config.database_config import DatabaseConfig, DatabaseType
from ...config.dw_config import DWConfig
from ...silver.transformation import create_syonet_transformations
from .tables_config_v3_migrated import get_syonet_tables_config  # ✅ Importa configuração migrada do V3


def create_syonet_system_config() -> SystemConfig:
    """
    Cria configuração completa do sistema Syonet
    ✅ Migrado do V3 com 100% de compatibilidade
    """
    
    # Configuração da origem (SQL Server)
    source_config = DatabaseConfig(
        name="syonet_source",
        type=DatabaseType.SQLSERVER,
        host="***********",
        port=50666,
        database="master",
        user="bq_dwcorporativo_u",
        password="N#OK+#{Yx*",
        pool_size=5,
        timeout=300,
        description="SQL Server Syonet - Sistema CRM"
    )
    
    # Configuração do sistema
    system_config = SystemConfig(
        name="syonet",
        description="Sistema CRM Syonet - Dados de vendas, clientes e eventos",
        source_connection=source_config,
        bronze_enabled=True,
        silver_enabled=True,
        gold_enabled=False,
        parallel_tables=6,
        chunk_size=50000,
        timeout_seconds=300,
        max_retries=3,
        retry_delay_minutes=1,
        incremental_fields=["dt_inc", "dt_alt"],  # Campos específicos do Syonet
        version="4.0.0",
        tags=["syonet", "crm", "vendas", "sqlserver"]
    )
    
    # ✅ Adiciona configurações de tabelas bronze migradas do V3
    _add_syonet_bronze_tables_v3_migrated(system_config)

    # Adiciona transformações silver
    silver_transformations = create_syonet_transformations()
    for transformation in silver_transformations:
        system_config.add_silver_transformation(transformation)

    return system_config


def _add_syonet_bronze_tables_v3_migrated(system_config: SystemConfig):
    """
    ✅ Adiciona configurações de tabelas do Syonet migradas do V3
    Usa a configuração completa migrada do TABLES_CONFIG das DAGs V3
    """
    # ✅ Obtém configuração migrada do V3 (100% compatível)
    tables_config = get_syonet_tables_config()
    
    # ✅ Adiciona todas as tabelas ao SystemConfig
    for table_config in tables_config:
        system_config.add_bronze_table(table_config)


def create_syonet_source_config() -> DatabaseConfig:
    """Cria configuração da origem Syonet (SQL Server)"""
    return DatabaseConfig(
        name="syonet_source",
        type=DatabaseType.SQLSERVER,
        host="***********",
        port=50666,
        database="master",
        user="bq_dwcorporativo_u",
        password="N#OK+#{Yx*",
        pool_size=5,
        timeout=300,
        description="SQL Server Syonet - Sistema CRM"
    )
