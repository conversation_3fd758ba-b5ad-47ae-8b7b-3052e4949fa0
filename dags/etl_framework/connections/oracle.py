"""
Implementação Oracle - ETL Framework

Implementação específica para Oracle Database.
"""

try:
    import cx_Oracle
    ORACLE_AVAILABLE = True
except ImportError:
    ORACLE_AVAILABLE = False

from typing import Op<PERSON>, <PERSON><PERSON>, Any
from .base import DatabaseConnection, DatabaseConfig


class OracleConnection(DatabaseConnection):
    """Implementação Oracle do DatabaseConnection"""
    
    def __init__(self, config: DatabaseConfig):
        if not ORACLE_AVAILABLE:
            raise ImportError("cx_Oracle não está instalado. Execute: pip install cx_Oracle")
        super().__init__(config)
    
    def _create_connection_string(self) -> str:
        """Cria string de conexão Oracle"""
        if self.config.extra_params and 'service_name' in self.config.extra_params:
            # Usando service name
            return f"{self.config.user}/{self.config.password}@{self.config.host}:{self.config.port}/{self.config.extra_params['service_name']}"
        else:
            # Usando SID
            return f"{self.config.user}/{self.config.password}@{self.config.host}:{self.config.port}/{self.config.database}"
    
    def _create_engine(self):
        """Cria pool de conexões Oracle"""
        connection_string = self._create_connection_string()
        
        return cx_Oracle.SessionPool(
            user=self.config.user,
            password=self.config.password,
            dsn=f"{self.config.host}:{self.config.port}/{self.config.database}",
            min=1,
            max=self.config.pool_size,
            increment=1,
            threaded=True
        )
    
    def _get_cursor(self, connection):
        """Retorna cursor Oracle"""
        return connection.cursor()
    
    def _execute_query(self, cursor, query: str, params: Optional[Tuple] = None):
        """Executa query Oracle"""
        if params:
            cursor.execute(query, params)
        else:
            cursor.execute(query)
        
        # Tenta retornar resultado se for SELECT
        try:
            return cursor.fetchall()
        except:
            # Query não retorna resultado (INSERT, UPDATE, DELETE, etc.)
            return cursor.rowcount
    
    def _get_table_exists_query(self, schema: str, table: str) -> str:
        """Query para verificar se tabela existe no Oracle"""
        return f"""
            SELECT CASE WHEN EXISTS (
                SELECT 1 FROM ALL_TABLES 
                WHERE OWNER = UPPER('{schema}') 
                AND TABLE_NAME = UPPER('{table}')
            ) THEN 1 ELSE 0 END FROM DUAL
        """
    
    def _get_count_query(self, schema: str, table: str) -> str:
        """Query para contar registros no Oracle"""
        return f"SELECT COUNT(*) FROM {schema}.{table}"
    
    def execute_dblink_query(self, cursor, dblink: str, query: str):
        """Executa query via database link (específico Oracle)"""
        dblink_sql = f"SELECT * FROM ({query})@{dblink}"
        cursor.execute(dblink_sql)
        return cursor.fetchall()


# Context manager para compatibilidade com código legado
def get_oracle_cursor(config: DatabaseConfig):
    """Context manager para compatibilidade com DAGs existentes"""
    conn = OracleConnection(config)
    return conn.get_cursor()


# Função para criar configuração a partir de dict (compatibilidade)
def create_oracle_config(config_dict: dict) -> DatabaseConfig:
    """Cria DatabaseConfig a partir de dicionário"""
    return DatabaseConfig(
        host=config_dict['host'],
        port=config_dict.get('port', 1521),
        database=config_dict.get('sid', config_dict.get('service_name')),
        user=config_dict['user'],
        password=config_dict['password'],
        timeout=config_dict.get('timeout', 300),
        pool_size=config_dict.get('pool_size', 15),
        extra_params={
            'service_name': config_dict.get('service_name'),
            'sid': config_dict.get('sid'),
            **config_dict.get('extra_params', {})
        }
    )
