"""
Classe Base para Conexões de Banco de Dados - ETL Framework

Classe abstrata que define a interface comum para todas as conexões de banco.
Implementa context manager, pool de conexões e configurações de timeout.
"""

from abc import ABC, abstractmethod
from contextlib import contextmanager
from typing import Dict, Any, Optional, Tuple
import logging
from dataclasses import dataclass


@dataclass
class DatabaseConfig:
    """Configuração de conexão com banco de dados"""
    host: str
    port: int
    database: str
    user: str
    password: str
    timeout: int = 300
    pool_size: int = 5
    max_overflow: int = 10
    extra_params: Optional[Dict[str, Any]] = None


class DatabaseConnection(ABC):
    """
    Classe abstrata para conexões de banco de dados
    
    Implementa padrões comuns:
    - Context manager para gerenciamento automático de conexões
    - Pool de conexões
    - Configurações de timeout
    - Logging estruturado
    """
    
    def __init__(self, config: DatabaseConfig):
        self.config = config
        self.logger = logging.getLogger(f"{self.__class__.__name__}")
        self._connection_pool = None
        
    @abstractmethod
    def _create_connection_string(self) -> str:
        """Cria string de conexão específica do SGBD"""
        pass
        
    @abstractmethod
    def _create_engine(self):
        """Cria engine de conexão específica do SGBD"""
        pass
        
    @abstractmethod
    def _get_cursor(self, connection):
        """Retorna cursor específico do SGBD"""
        pass
        
    @abstractmethod
    def _execute_query(self, cursor, query: str, params: Optional[Tuple] = None):
        """Executa query específica do SGBD"""
        pass
        
    @abstractmethod
    def _get_table_exists_query(self, schema: str, table: str) -> str:
        """Retorna query para verificar se tabela existe"""
        pass
        
    @abstractmethod
    def _get_count_query(self, schema: str, table: str) -> str:
        """Retorna query para contar registros"""
        pass
        
    def initialize_pool(self):
        """Inicializa pool de conexões"""
        if self._connection_pool is None:
            self._connection_pool = self._create_engine()
            self.logger.info(f"Pool de conexões inicializado para {self.config.host}")
            
    @contextmanager
    def get_connection(self):
        """Context manager para obter conexão do pool"""
        if self._connection_pool is None:
            self.initialize_pool()
            
        connection = None
        try:
            # ✅ Correção sistêmica: Diferentes pools usam métodos diferentes
            if hasattr(self._connection_pool, 'getconn'):
                # psycopg2.pool.ThreadedConnectionPool usa getconn()
                connection = self._connection_pool.getconn()
            else:
                # Outros pools podem usar connect()
                connection = self._connection_pool.connect()

            self.logger.debug(f"Conexão obtida do pool")
            yield connection
        except Exception as e:
            if connection:
                connection.rollback()
            self.logger.error(f"Erro na conexão: {str(e)}")
            raise
        finally:
            if connection:
                # ✅ Correção sistêmica: Diferentes pools usam métodos diferentes para retornar conexões
                if hasattr(self._connection_pool, 'putconn'):
                    # psycopg2.pool.ThreadedConnectionPool usa putconn()
                    self._connection_pool.putconn(connection)
                else:
                    # Outros pools podem usar close()
                    connection.close()

                self.logger.debug(f"Conexão retornada ao pool")
                
    @contextmanager
    def get_cursor(self):
        """Context manager para obter cursor"""
        with self.get_connection() as connection:
            cursor = None
            try:
                cursor = self._get_cursor(connection)
                yield cursor, connection
            except Exception as e:
                if connection:
                    connection.rollback()
                raise
            finally:
                if cursor:
                    cursor.close()
                    
    def execute_query(self, query: str, params: Optional[Tuple] = None) -> Any:
        """Executa query e retorna resultado"""
        with self.get_cursor() as (cursor, connection):
            result = self._execute_query(cursor, query, params)
            connection.commit()
            return result
            
    def table_exists(self, schema: str, table: str) -> bool:
        """Verifica se tabela existe"""
        query = self._get_table_exists_query(schema, table)
        with self.get_cursor() as (cursor, connection):
            cursor.execute(query)
            result = cursor.fetchone()
            return bool(result[0]) if result else False
            
    def get_table_count(self, schema: str, table: str) -> int:
        """Retorna contagem de registros da tabela"""
        query = self._get_count_query(schema, table)
        with self.get_cursor() as (cursor, connection):
            cursor.execute(query)
            result = cursor.fetchone()
            return int(result[0]) if result else 0
            
    def close_pool(self):
        """Fecha pool de conexões"""
        if self._connection_pool:
            # ✅ Correção sistêmica: Diferentes pools usam métodos diferentes para fechar
            if hasattr(self._connection_pool, 'closeall'):
                # psycopg2.pool.ThreadedConnectionPool usa closeall()
                self._connection_pool.closeall()
            elif hasattr(self._connection_pool, 'dispose'):
                # SQLAlchemy pools usam dispose()
                self._connection_pool.dispose()
            else:
                # Fallback genérico
                try:
                    self._connection_pool.close()
                except:
                    pass

            self._connection_pool = None
            self.logger.info("Pool de conexões fechado")

    def test_connection(self) -> bool:
        """
        Testa a conexão com o banco de dados

        Returns:
            bool: True se conexão bem-sucedida, False caso contrário
        """
        try:
            with self.get_cursor() as (cursor, connection):
                # Query simples para testar conexão
                cursor.execute("SELECT 1")
                result = cursor.fetchone()

                if result:
                    self.logger.info(f"✅ Conexão testada com sucesso para {self.config.host}:{self.config.port}")
                    return True
                else:
                    self.logger.error(f"❌ Falha no teste de conexão para {self.config.host}:{self.config.port}")
                    return False

        except Exception as e:
            self.logger.error(f"❌ Erro ao testar conexão para {self.config.host}:{self.config.port}: {str(e)}")
            return False
