"""
Implementação SQL Server - ETL Framework

Implementação específica para SQL Server baseada na lógica das DAGs V3.
"""

import pymssql
from typing import Op<PERSON>, Tuple, Any
from .base import DatabaseConnection, DatabaseConfig


def _create_robust_sqlserver_connection(config: DatabaseConfig):
    """
    Cria conexão SQL Server robusta com múltiplas tentativas

    Resolve o erro: invalid dsn: invalid connection option "database"
    """
    connection_attempts = [
        # Tentativa 1: Sintaxe padrão pymssql
        {
            'server': config.host,
            'port': config.port,
            'database': config.database,
            'user': config.user,
            'password': config.password,
            'timeout': config.timeout,
            'charset': 'utf8'
        },
        # Tentativa 2: Sem porta explícita (deixa pymssql decidir)
        {
            'server': config.host,
            'database': config.database,
            'user': config.user,
            'password': config.password,
            'timeout': config.timeout,
            'charset': 'utf8'
        },
        # Tentativa 3: Com host:porta como server
        {
            'server': f"{config.host}:{config.port}",
            'database': config.database,
            'user': config.user,
            'password': config.password,
            'timeout': config.timeout,
            'charset': 'utf8'
        },
        # Tentativa 4: Sem charset
        {
            'server': config.host,
            'port': config.port,
            'database': config.database,
            'user': config.user,
            'password': config.password,
            'timeout': config.timeout
        }
    ]

    last_error = None
    for i, params in enumerate(connection_attempts, 1):
        try:
            # Remove parâmetros None para evitar problemas
            clean_params = {k: v for k, v in params.items() if v is not None}
            connection = pymssql.connect(**clean_params)

            if i > 1:
                print(f"✅ Conexão SQL Server bem-sucedida na tentativa {i}")

            return connection

        except Exception as e:
            last_error = e
            print(f"❌ Tentativa {i} falhou: {str(e)}")
            continue

    # Se todas as tentativas falharam
    raise Exception(f"Falha em todas as tentativas de conexão SQL Server: {last_error}")


class SQLServerConnection(DatabaseConnection):
    """Implementação SQL Server do DatabaseConnection"""
    
    def _create_connection_string(self) -> str:
        """Cria string de conexão SQL Server"""
        return (
            f"server={self.config.host};"
            f"port={self.config.port};"
            f"database={self.config.database};"
            f"user={self.config.user};"
            f"password={self.config.password};"
            f"timeout={self.config.timeout}"
        )
    
    def _create_engine(self):
        """Cria pool de conexões SQL Server (simulado)"""
        # pymssql não tem pool nativo, implementamos um simples
        return SQLServerConnectionPool(self.config)
    
    def _get_cursor(self, connection):
        """Retorna cursor SQL Server"""
        return connection.cursor()
    
    def _execute_query(self, cursor, query: str, params: Optional[Tuple] = None):
        """Executa query SQL Server"""
        if params:
            cursor.execute(query, params)
        else:
            cursor.execute(query)
        
        # Tenta retornar resultado se for SELECT
        try:
            return cursor.fetchall()
        except:
            # Query não retorna resultado (INSERT, UPDATE, DELETE, etc.)
            return cursor.rowcount
    
    def _get_table_exists_query(self, schema: str, table: str) -> str:
        """Query para verificar se tabela existe no SQL Server"""
        return f"""
            SELECT CASE WHEN EXISTS (
                SELECT 1 FROM INFORMATION_SCHEMA.TABLES 
                WHERE TABLE_SCHEMA = '{schema}' 
                AND TABLE_NAME = '{table}'
            ) THEN 1 ELSE 0 END
        """
    
    def _get_count_query(self, schema: str, table: str) -> str:
        """
        Query para contar registros no SQL Server
        ✅ Correção sistêmica: Usar OPENQUERY para acessar dados do PostgreSQL
        """
        # Para SQL Server acessando PostgreSQL via linked server, usar OPENQUERY
        return f"SELECT cnt FROM OPENQUERY(POSTGRES, 'SELECT COUNT(*) AS cnt FROM {schema}.{table}')"
    
    def execute_openquery(self, cursor, linked_server: str, query: str):
        """Executa OPENQUERY (específico SQL Server)"""
        openquery_sql = f"SELECT * FROM OPENQUERY({linked_server}, '{query}')"
        cursor.execute(openquery_sql)
        return cursor.fetchall()


class SQLServerConnectionPool:
    """Pool de conexões simples para SQL Server"""
    
    def __init__(self, config: DatabaseConfig):
        self.config = config
        self._connections = []
        self._max_connections = config.pool_size
    
    def connect(self):
        """Obtém conexão do pool"""
        if self._connections:
            return self._connections.pop()

        # ✅ Correção sistêmica: Usa função robusta de conexão
        return _create_robust_sqlserver_connection(self.config)
    
    def return_connection(self, connection):
        """Retorna conexão ao pool"""
        if len(self._connections) < self._max_connections:
            self._connections.append(connection)
        else:
            connection.close()
    
    def dispose(self):
        """Fecha todas as conexões do pool"""
        for conn in self._connections:
            conn.close()
        self._connections.clear()


# Context manager para compatibilidade com código legado
def get_sql_server_cursor(config: DatabaseConfig):
    """Context manager para compatibilidade com DAGs existentes"""
    conn = SQLServerConnection(config)
    return conn.get_cursor()


# Função para criar configuração a partir de dict (compatibilidade)
def create_sqlserver_config(config_dict: dict) -> DatabaseConfig:
    """Cria DatabaseConfig a partir de dicionário"""
    return DatabaseConfig(
        host=config_dict['server'],
        port=config_dict['port'],
        database=config_dict['database'],
        user=config_dict['user'],
        password=config_dict['password'],
        timeout=config_dict.get('timeout', 300),
        pool_size=config_dict.get('pool_size', 15),
        extra_params=config_dict.get('extra_params')
    )
