"""
Implementação PostgreSQL - ETL Framework

Implementação específica para PostgreSQL baseada na lógica das DAGs V3.
"""

import psycopg2
import psycopg2.pool
from typing import Optional, Tuple, Any
from .base import DatabaseConnection, DatabaseConfig


class PostgreSQLConnection(DatabaseConnection):
    """Implementação PostgreSQL do DatabaseConnection"""
    
    def _create_connection_string(self) -> str:
        """Cria string de conexão PostgreSQL"""
        # ✅ Correção sistêmica: psycopg2 usa 'dbname' ao invés de 'database'
        return (
            f"host={self.config.host} "
            f"port={self.config.port} "
            f"dbname={self.config.database} "  # ✅ Corrigido: dbname ao invés de database
            f"user={self.config.user} "
            f"password={self.config.password}"
        )
    
    def _create_engine(self):
        """Cria pool de conexões PostgreSQL"""
        connection_string = self._create_connection_string()
        
        return psycopg2.pool.ThreadedConnectionPool(
            minconn=1,
            maxconn=self.config.pool_size,
            dsn=connection_string
        )
    
    def _get_cursor(self, connection):
        """Retorna cursor PostgreSQL"""
        return connection.cursor()
    
    def _execute_query(self, cursor, query: str, params: Optional[Tuple] = None):
        """Executa query PostgreSQL"""
        if params:
            cursor.execute(query, params)
        else:
            cursor.execute(query)
        
        # Tenta retornar resultado se for SELECT
        try:
            return cursor.fetchall()
        except psycopg2.ProgrammingError:
            # Query não retorna resultado (INSERT, UPDATE, DELETE, etc.)
            return cursor.rowcount
    
    def _get_table_exists_query(self, schema: str, table: str) -> str:
        """Query para verificar se tabela existe no PostgreSQL"""
        return f"""
            SELECT EXISTS (
                SELECT 1 FROM information_schema.tables 
                WHERE table_schema = '{schema}' 
                AND table_name = '{table}'
            )
        """
    
    def _get_count_query(self, schema: str, table: str) -> str:
        """Query para contar registros no PostgreSQL"""
        return f"SELECT COUNT(*) FROM {schema}.{table}"
    
    @property
    def get_connection_direct(self):
        """Método para compatibilidade com código legado"""
        return self.get_connection()
    
    def execute_copy_expert(self, cursor, sql: str, file_obj):
        """Executa COPY usando copy_expert (específico PostgreSQL)"""
        cursor.copy_expert(sql, file_obj)
    
    def set_statement_timeout(self, cursor, timeout_seconds: int):
        """Define timeout para statements (específico PostgreSQL)"""
        cursor.execute(f"SET statement_timeout = '{timeout_seconds}s'")
    
    def apply_grants(self, cursor, table_name: str, grants: list):
        """Aplica grants na tabela (específico PostgreSQL)"""
        for grant in grants:
            try:
                cursor.execute(grant)
                self.logger.debug(f"Grant aplicado: {grant}")
            except Exception as e:
                self.logger.warning(f"Erro ao aplicar grant '{grant}': {str(e)}")


# Context manager para compatibilidade com código legado
def get_postgres_cursor(config: DatabaseConfig):
    """Context manager para compatibilidade com DAGs existentes"""
    conn = PostgreSQLConnection(config)
    return conn.get_cursor()


# Função para criar configuração a partir de dict (compatibilidade)
def create_postgres_config(config_dict: dict) -> DatabaseConfig:
    """Cria DatabaseConfig a partir de dicionário"""
    return DatabaseConfig(
        host=config_dict['host'],
        port=config_dict['port'],
        database=config_dict['database'],
        user=config_dict['user'],
        password=config_dict['password'],
        timeout=config_dict.get('timeout', 300),
        pool_size=config_dict.get('pool_size', 5),
        extra_params=config_dict.get('extra_params')
    )
