"""
Processador Silver - ETL Framework

Processador genérico da camada Silver que executa transformações CREATE TABLE AS
dentro do Data Warehouse Corporativo.
"""

import logging
import time
from typing import Dict, Any, List, Optional
from datetime import datetime
from etl_framework.connections.postgres import PostgreSQLConnection
from etl_framework.config.dw_config import DWConfig
from etl_framework.config.system_config import SystemConfig
from etl_framework.utils.logger import ETLLogger
from etl_framework.utils.timeout_manager import BatchTimer
from etl_framework.silver.transformation import SilverTransformation
from etl_framework.silver.validator import SilverValidator


class SilverProcessor:
    """
    Processador genérico da camada Silver
    
    Executa transformações CREATE TABLE AS dentro do DW para qualquer sistema:
    - Syonet: tb_oportunidades_base, tb_IM_Assinatura_original
    - Oracle ERP: tb_vendas_consolidadas, tb_estoque_atual
    - Sistema X: tb_clientes_enriquecidos, tb_produtos_ativos
    
    Características:
    - Execução dentro do DW (PostgreSQL)
    - Queries SQL complexas
    - Dependências entre transformações
    - Validação automática
    - Aplicação de permissões
    """
    
    def __init__(self, 
                 system_config: SystemConfig,
                 dw_config: DWConfig):
        
        self.system_config = system_config
        self.dw_config = dw_config
        
        # Conexão única com DW (silver executa apenas no DW)
        self.dw_connection = PostgreSQLConnection(dw_config.connection)
        
        # Logger específico do sistema
        self.logger = ETLLogger(f"SilverProcessor_{system_config.name}")
        
        # Validador
        self.validator = SilverValidator(
            dw_connection=self.dw_connection,
            system_config=system_config,
            dw_config=dw_config
        )
        
        # Métricas
        self.batch_timer = BatchTimer(f"silver_{system_config.name}")
        self.results = {}
    
    def process_all_transformations(self) -> Dict[str, Any]:
        """
        Processa todas as transformações silver do sistema
        """
        self.logger.log_dag_start(f"silver_{self.system_config.name}")
        self.batch_timer.start_batch()
        
        try:
            # Obtém transformações na ordem de dependência
            transformations = self.system_config.get_silver_transformations()
            
            self.logger.info(f"🚀 Iniciando processamento silver de {len(transformations)} transformações do sistema {self.system_config.name}")
            
            # Processa cada transformação
            for transformation in transformations:
                try:
                    # Verifica dependências
                    if not self._check_dependencies(transformation):
                        self.logger.warning(f"⚠️ Dependências não atendidas para {transformation.name}")
                        continue
                    
                    self.batch_timer.start_operation(transformation.name)
                    result = self.process_single_transformation(transformation)
                    self.results[transformation.name] = result
                    self.batch_timer.end_operation(transformation.name)
                    
                except Exception as e:
                    self.logger.log_table_error(transformation.name, str(e))
                    self.results[transformation.name] = {
                        'success': False,
                        'error': str(e),
                        'records_processed': 0
                    }
                    self.batch_timer.end_operation(transformation.name)
            
            # Finaliza processamento
            batch_stats = self.batch_timer.end_batch()
            successful_transformations = sum(1 for r in self.results.values() if r.get('success', False))
            
            self.logger.log_dag_complete(f"silver_{self.system_config.name}", successful_transformations)
            
            return {
                'success': True,
                'system': self.system_config.name,
                'transformations_processed': len(transformations),
                'successful_transformations': successful_transformations,
                'failed_transformations': len(transformations) - successful_transformations,
                'batch_stats': batch_stats,
                'transformation_results': self.results
            }
            
        except Exception as e:
            self.logger.log_dag_error(f"silver_{self.system_config.name}", str(e))
            return {
                'success': False,
                'system': self.system_config.name,
                'error': str(e),
                'transformation_results': self.results
            }
    
    def process_single_transformation(self, transformation: SilverTransformation) -> Dict[str, Any]:
        """
        Processa uma única transformação silver
        """
        self.logger.log_table_start(transformation.name, "silver_transformation")
        
        start_time = time.time()
        
        try:
            with self.dw_connection.get_cursor() as (cursor, conn):
                # Configura timeout
                cursor.execute(f"SET statement_timeout = '{transformation.timeout_seconds}s'")
                
                # Executa transformação (CREATE TABLE AS)
                self.logger.info(f"🔄 Executando transformação {transformation.name}")
                cursor.execute(transformation.sql)
                conn.commit()
                
                # Aplica permissões se configuradas
                if transformation.grants:
                    self._apply_grants(cursor, transformation)
                    conn.commit()
            
            elapsed_time = time.time() - start_time
            
            # Validação
            count = self._validate_transformation(transformation)
            
            self.logger.log_table_success(transformation.name, count, time=f"{elapsed_time:.1f}s")
            
            return {
                'success': True,
                'records_processed': count,
                'execution_time': elapsed_time,
                'transformation_name': transformation.name
            }
            
        except Exception as e:
            elapsed_time = time.time() - start_time
            self.logger.log_table_error(transformation.name, str(e), time=f"{elapsed_time:.1f}s")
            raise
    
    def _check_dependencies(self, transformation: SilverTransformation) -> bool:
        """
        Verifica se as dependências da transformação estão atendidas
        """
        if not transformation.dependencies:
            return True
        
        try:
            with self.dw_connection.get_cursor() as (cursor, conn):
                for dependency in transformation.dependencies:
                    # Verifica se tabela de dependência existe e tem dados
                    cursor.execute(f"""
                        SELECT EXISTS (
                            SELECT 1 FROM information_schema.tables 
                            WHERE table_name = '{dependency}'
                        )
                    """)
                    
                    if not cursor.fetchone()[0]:
                        self.logger.warning(f"⚠️ Dependência não encontrada: {dependency}")
                        return False
                    
                    # Verifica se tem dados (opcional)
                    cursor.execute(f"SELECT COUNT(*) FROM {dependency}")
                    count = cursor.fetchone()[0]
                    
                    if count == 0:
                        self.logger.warning(f"⚠️ Dependência vazia: {dependency} (0 registros)")
                        # Não bloqueia, apenas avisa
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Erro ao verificar dependências: {str(e)}")
            return False
    
    def _apply_grants(self, cursor, transformation: SilverTransformation):
        """Aplica grants configurados na transformação"""
        table_name = self.dw_config.get_table_name(
            self.system_config.name, 
            transformation.name, 
            "silver"
        )
        
        grants_applied = 0
        for grant_template in transformation.grants:
            try:
                grant_sql = grant_template.replace('{table_name}', table_name)
                cursor.execute(grant_sql)
                grants_applied += 1
                self.logger.debug(f"✅ Grant aplicado: {grant_sql}")
            except Exception as e:
                self.logger.warning(f"⚠️ Erro ao aplicar grant: {str(e)}")
        
        self.logger.info(f"🔐 {grants_applied} grants aplicados para {transformation.name}")
    
    def _validate_transformation(self, transformation: SilverTransformation) -> int:
        """Valida resultado da transformação"""
        try:
            table_name = self.dw_config.get_table_name(
                self.system_config.name, 
                transformation.name, 
                "silver"
            )
            
            with self.dw_connection.get_cursor() as (cursor, conn):
                cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                result = cursor.fetchone()
                count = result[0] if result else 0
                
                if count == 0:
                    self.logger.warning(f"⚠️ AVISO: {transformation.name} está vazia - verificar filtros e dependências")
                else:
                    self.logger.info(f"✅ {transformation.name} validada com {count:,} registros")
                
                return count
                
        except Exception as e:
            self.logger.error(f"❌ Erro na validação de {transformation.name}: {str(e)}")
            raise
    
    def get_processing_summary(self) -> Dict[str, Any]:
        """Retorna resumo do processamento silver"""
        if not self.results:
            return {'message': 'Nenhum processamento executado'}
        
        successful = [name for name, result in self.results.items() if result.get('success', False)]
        failed = [name for name, result in self.results.items() if not result.get('success', False)]
        
        total_records = sum(result.get('records_processed', 0) for result in self.results.values())
        
        return {
            'system': self.system_config.name,
            'total_transformations': len(self.results),
            'successful_transformations': len(successful),
            'failed_transformations': len(failed),
            'success_rate': len(successful) / len(self.results) * 100,
            'total_records_processed': total_records,
            'successful_transformation_names': successful,
            'failed_transformation_names': failed
        }
    
    def cleanup(self):
        """Limpa recursos"""
        try:
            if hasattr(self.dw_connection, 'close_pool'):
                self.dw_connection.close_pool()
            self.logger.info("🧹 Recursos limpos com sucesso")
        except Exception as e:
            self.logger.warning(f"⚠️ Erro na limpeza de recursos: {str(e)}")

    def get_transformations_with_dependencies(self) -> Dict[str, List[str]]:
        """
        Retorna mapeamento de transformações e suas dependências

        Returns:
            Dict[str, List[str]]: {transformation_name: [dependencies]}
        """
        transformations = self.system_config.get_silver_transformations()
        dependency_map = {}

        for transformation in transformations:
            # Filtra dependências que são outras transformações silver (não bronze)
            silver_dependencies = []
            for dep in transformation.dependencies:
                # Se não tem prefixo bronze_, é uma dependência silver
                if not dep.startswith('bronze_'):
                    silver_dependencies.append(dep)

            dependency_map[transformation.name] = silver_dependencies

        return dependency_map

    def get_transformations_for_parallel_processing(self) -> List:
        """
        Retorna transformações organizadas para processamento paralelo

        Returns:
            List[SilverTransformation]: Lista de transformações sem dependências silver
        """
        transformations = self.system_config.get_silver_transformations()
        dependency_map = self.get_transformations_with_dependencies()

        # Retorna transformações que não dependem de outras transformações silver
        independent_transformations = []
        for transformation in transformations:
            if not dependency_map.get(transformation.name, []):
                independent_transformations.append(transformation)

        return independent_transformations

    def get_dependent_transformations(self, completed_transformation: str) -> List:
        """
        Retorna transformações que dependem da transformação completada

        Args:
            completed_transformation: Nome da transformação que foi completada

        Returns:
            List[SilverTransformation]: Lista de transformações dependentes
        """
        transformations = self.system_config.get_silver_transformations()
        dependency_map = self.get_transformations_with_dependencies()

        dependent_transformations = []
        for transformation in transformations:
            dependencies = dependency_map.get(transformation.name, [])
            if completed_transformation in dependencies:
                dependent_transformations.append(transformation)

        return dependent_transformations


# Factory function para criar processador silver baseado no sistema
def create_silver_processor(system_name: str, dw_config: DWConfig) -> SilverProcessor:
    """
    Factory para criar processador silver baseado no sistema
    """
    # Import dinâmico da configuração do sistema
    if system_name.lower() == 'syonet':
        from etl_framework.systems.syonet.config import create_syonet_system_config
        system_config = create_syonet_system_config(dw_config)

    elif system_name.lower() == 'oracle_erp':
        from etl_framework.systems.oracle_erp.config import create_oracle_erp_system_config
        system_config = create_oracle_erp_system_config(dw_config)

    else:
        raise ValueError(f"Sistema não suportado: {system_name}")
    
    return SilverProcessor(
        system_config=system_config,
        dw_config=dw_config
    )
